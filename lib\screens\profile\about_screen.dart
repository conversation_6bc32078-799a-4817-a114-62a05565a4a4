import 'package:flutter/material.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_text_styles.dart';
import '../../constants/app_dimensions.dart';

class AboutScreen extends StatefulWidget {
  const AboutScreen({super.key});

  @override
  State<AboutScreen> createState() => _AboutScreenState();
}

class _AboutScreenState extends State<AboutScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundSecondary,
      appBar: AppBar(
        title: const Text('Về ứng dụng'),
        backgroundColor: const Color(0xFF007A33),
        foregroundColor: AppColors.textWhite,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppDimensions.paddingL),
        child: Column(
          children: [
            _buildAppInfoSection(),
            const SizedBox(height: AppDimensions.paddingL),
            _buildVersionSection(),
            const SizedBox(height: AppDimensions.paddingL),
            _buildLegalSection(),
            const SizedBox(height: AppDimensions.paddingL),
            _buildContactSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildAppInfoSection() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.textWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: const Color(0xFF007A33),
              borderRadius: BorderRadius.circular(20),
            ),
            child: const Icon(
              Icons.account_balance,
              size: 40,
              color: AppColors.textWhite,
            ),
          ),
          const SizedBox(height: AppDimensions.paddingL),

          Text(
            'VietcomBank Mobile',
            style: AppTextStyles.heading3.copyWith(
              color: const Color(0xFF007A33),
              fontWeight: FontWeight.bold,
            ),
          ),

          const SizedBox(height: AppDimensions.paddingS),

          Text(
            'Ngân hàng TMCP Ngoại thương Việt Nam',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: AppDimensions.paddingL),

          Text(
            'Ứng dụng ngân hàng số hiện đại, an toàn và tiện lợi. '
            'Cung cấp đầy đủ các dịch vụ ngân hàng trực tuyến với '
            'giao diện thân thiện và bảo mật cao.',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textPrimary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildVersionSection() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.textWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Thông tin phiên bản',
            style: AppTextStyles.heading4.copyWith(
              color: const Color(0xFF007A33),
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppDimensions.paddingL),

          _buildInfoRow('Phiên bản', '1.0.0'),
          _buildInfoRow('Build', '100'),
          _buildInfoRow('Ngày phát hành', '15/01/2024'),
          _buildInfoRow('Hệ điều hành', 'iOS 14.0+, Android 8.0+'),
          _buildInfoRow('Kích thước', '45.2 MB'),
        ],
      ),
    );
  }

  Widget _buildLegalSection() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.textWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Pháp lý',
            style: AppTextStyles.heading4.copyWith(
              color: const Color(0xFF007A33),
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppDimensions.paddingL),

          _buildLegalItem(
            title: 'Điều khoản sử dụng',
            onTap: () => _showLegalDialog('Điều khoản sử dụng', context),
          ),

          const Divider(height: 32),

          _buildLegalItem(
            title: 'Chính sách bảo mật',
            onTap: () => _showLegalDialog('Chính sách bảo mật', context),
          ),

          const Divider(height: 32),

          _buildLegalItem(
            title: 'Giấy phép hoạt động',
            onTap: () => _showLegalDialog('Giấy phép hoạt động', context),
          ),

          const Divider(height: 32),

          _buildLegalItem(
            title: 'Thông tin bản quyền',
            onTap: () => _showLegalDialog('Thông tin bản quyền', context),
          ),
        ],
      ),
    );
  }

  Widget _buildContactSection() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.textWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Liên hệ',
            style: AppTextStyles.heading4.copyWith(
              color: const Color(0xFF007A33),
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppDimensions.paddingL),

          _buildContactItem(
            icon: Icons.web,
            title: 'Website',
            subtitle: 'www.vietcombank.com.vn',
          ),

          const SizedBox(height: AppDimensions.paddingM),

          _buildContactItem(
            icon: Icons.phone,
            title: 'Hotline',
            subtitle: '1900 545 413',
          ),

          const SizedBox(height: AppDimensions.paddingM),

          _buildContactItem(
            icon: Icons.email,
            title: 'Email',
            subtitle: '<EMAIL>',
          ),

          const SizedBox(height: AppDimensions.paddingM),

          _buildContactItem(
            icon: Icons.location_on,
            title: 'Trụ sở chính',
            subtitle: '198 Trần Quang Khải, Hoàn Kiếm, Hà Nội',
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppDimensions.paddingM),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          Text(
            value,
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLegalItem({required String title, required VoidCallback onTap}) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(AppDimensions.radiusS),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: AppDimensions.paddingS),
        child: Row(
          children: [
            Expanded(
              child: Text(
                title,
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            const Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: AppColors.textSecondary,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactItem({
    required IconData icon,
    required String title,
    required String subtitle,
  }) {
    return Row(
      children: [
        Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: const Color(0xFF007A33).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          ),
          child: Icon(
            icon,
            color: const Color(0xFF007A33),
            size: AppDimensions.iconM,
          ),
        ),
        const SizedBox(width: AppDimensions.paddingM),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _showLegalDialog(String title, BuildContext context) {
    // This would typically show the actual legal document
    // For demo purposes, we'll show a placeholder
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text('Nội dung $title sẽ được hiển thị ở đây.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Đóng'),
          ),
        ],
      ),
    );
  }
}
