class BankCard {
  final String id;
  final String cardNumber;
  final String cardHolderName;
  final String expiryDate;
  final String cvv;
  final CardType cardType;
  final CardStatus status;
  final double balance;
  final double creditLimit;
  final double availableCredit;
  final bool isBlocked;
  final bool isContactless;
  final String issueDate;
  final List<CardTransaction> recentTransactions;

  BankCard({
    required this.id,
    required this.cardNumber,
    required this.cardHolderName,
    required this.expiryDate,
    required this.cvv,
    required this.cardType,
    required this.status,
    required this.balance,
    required this.creditLimit,
    required this.availableCredit,
    required this.isBlocked,
    required this.isContactless,
    required this.issueDate,
    required this.recentTransactions,
  });

  String get maskedCardNumber {
    if (cardNumber.length >= 16) {
      return '**** **** **** ${cardNumber.substring(12)}';
    }
    return cardNumber;
  }

  String get cardBrand {
    if (cardNumber.startsWith('4')) return 'Visa';
    if (cardNumber.startsWith('5')) return 'Mastercard';
    if (cardNumber.startsWith('3')) return 'American Express';
    return 'Unknown';
  }

  factory BankCard.fromJson(Map<String, dynamic> json) {
    return BankCard(
      id: json['id'],
      cardNumber: json['cardNumber'],
      cardHolderName: json['cardHolderName'],
      expiryDate: json['expiryDate'],
      cvv: json['cvv'],
      cardType: CardType.values.firstWhere(
        (e) => e.toString() == 'CardType.${json['cardType']}',
        orElse: () => CardType.debit,
      ),
      status: CardStatus.values.firstWhere(
        (e) => e.toString() == 'CardStatus.${json['status']}',
        orElse: () => CardStatus.active,
      ),
      balance: json['balance']?.toDouble() ?? 0.0,
      creditLimit: json['creditLimit']?.toDouble() ?? 0.0,
      availableCredit: json['availableCredit']?.toDouble() ?? 0.0,
      isBlocked: json['isBlocked'] ?? false,
      isContactless: json['isContactless'] ?? true,
      issueDate: json['issueDate'],
      recentTransactions: (json['recentTransactions'] as List?)
          ?.map((e) => CardTransaction.fromJson(e))
          .toList() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'cardNumber': cardNumber,
      'cardHolderName': cardHolderName,
      'expiryDate': expiryDate,
      'cvv': cvv,
      'cardType': cardType.toString().split('.').last,
      'status': status.toString().split('.').last,
      'balance': balance,
      'creditLimit': creditLimit,
      'availableCredit': availableCredit,
      'isBlocked': isBlocked,
      'isContactless': isContactless,
      'issueDate': issueDate,
      'recentTransactions': recentTransactions.map((e) => e.toJson()).toList(),
    };
  }
}

enum CardType {
  debit,
  credit,
  prepaid,
}

enum CardStatus {
  active,
  blocked,
  expired,
  pending,
}

class CardTransaction {
  final String id;
  final String merchantName;
  final double amount;
  final DateTime date;
  final String category;
  final TransactionType type;
  final String location;

  CardTransaction({
    required this.id,
    required this.merchantName,
    required this.amount,
    required this.date,
    required this.category,
    required this.type,
    required this.location,
  });

  factory CardTransaction.fromJson(Map<String, dynamic> json) {
    return CardTransaction(
      id: json['id'],
      merchantName: json['merchantName'],
      amount: json['amount']?.toDouble() ?? 0.0,
      date: DateTime.parse(json['date']),
      category: json['category'],
      type: TransactionType.values.firstWhere(
        (e) => e.toString() == 'TransactionType.${json['type']}',
        orElse: () => TransactionType.purchase,
      ),
      location: json['location'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'merchantName': merchantName,
      'amount': amount,
      'date': date.toIso8601String(),
      'category': category,
      'type': type.toString().split('.').last,
      'location': location,
    };
  }
}

enum TransactionType {
  purchase,
  withdrawal,
  refund,
  fee,
}
