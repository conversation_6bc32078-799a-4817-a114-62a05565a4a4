import 'package:flutter/material.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_text_styles.dart';
import '../../constants/app_dimensions.dart';
import '../../widgets/custom_text_field.dart';
import '../../widgets/custom_button.dart';
import '../../models/savings_model.dart';

class CreateSavingsScreen extends StatefulWidget {
  const CreateSavingsScreen({super.key});

  @override
  State<CreateSavingsScreen> createState() => _CreateSavingsScreenState();
}

class _CreateSavingsScreenState extends State<CreateSavingsScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _amountController = TextEditingController();
  final _monthlyController = TextEditingController();
  SavingsType _selectedType = SavingsType.regular;
  int _selectedTerm = 12; // months
  bool _isLoading = false;

  final List<Map<String, dynamic>> _savingsTypes = [
    {
      'type': SavingsType.regular,
      'name': 'Tiết kiệm thường',
      'description': 'Gửi và rút linh hoạt',
      'rate': 5.5,
      'icon': Icons.savings,
    },
    {
      'type': SavingsType.fixedTerm,
      'name': 'Gửi có kỳ hạn',
      'description': 'Lãi suất cao, không rút trước hạn',
      'rate': 7.2,
      'icon': Icons.schedule,
    },
    {
      'type': SavingsType.goalBased,
      'name': 'Tiết kiệm mục tiêu',
      'description': 'Tiết kiệm cho mục tiêu cụ thể',
      'rate': 6.0,
      'icon': Icons.flag,
    },
    {
      'type': SavingsType.automatic,
      'name': 'Tiết kiệm tự động',
      'description': 'Tự động chuyển tiền hàng tháng',
      'rate': 6.5,
      'icon': Icons.autorenew,
    },
  ];

  final List<int> _terms = [6, 12, 18, 24, 36];

  @override
  void dispose() {
    _nameController.dispose();
    _amountController.dispose();
    _monthlyController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundSecondary,
      appBar: AppBar(
        title: const Text('Mở sổ tiết kiệm'),
        backgroundColor: AppColors.backgroundPrimary,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppDimensions.paddingL),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildTypeSelection(),
              const SizedBox(height: AppDimensions.paddingL),
              _buildAccountForm(),
              const SizedBox(height: AppDimensions.paddingL),
              if (_selectedType == SavingsType.fixedTerm) _buildTermSelection(),
              if (_selectedType == SavingsType.fixedTerm) 
                const SizedBox(height: AppDimensions.paddingL),
              _buildInterestInfo(),
              const SizedBox(height: AppDimensions.paddingXL),
              _buildCreateButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTypeSelection() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.backgroundPrimary,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Chọn loại tiết kiệm',
            style: AppTextStyles.heading4,
          ),
          const SizedBox(height: AppDimensions.paddingM),
          ...(_savingsTypes.map((type) => _buildTypeCard(type))),
        ],
      ),
    );
  }

  Widget _buildTypeCard(Map<String, dynamic> type) {
    final isSelected = _selectedType == type['type'];
    
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedType = type['type'];
        });
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: AppDimensions.paddingM),
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        decoration: BoxDecoration(
          color: isSelected 
              ? AppColors.primaryBlue.withValues(alpha: 0.1)
              : AppColors.backgroundGray,
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          border: Border.all(
            color: isSelected 
                ? AppColors.primaryBlue
                : AppColors.borderLight,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: isSelected 
                    ? AppColors.primaryBlue
                    : AppColors.textSecondary,
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              ),
              child: Icon(
                type['icon'],
                color: AppColors.textWhite,
                size: AppDimensions.iconM,
              ),
            ),
            const SizedBox(width: AppDimensions.paddingM),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    type['name'],
                    style: AppTextStyles.bodyMedium.copyWith(
                      fontWeight: FontWeight.w600,
                      color: isSelected 
                          ? AppColors.primaryBlue
                          : AppColors.textPrimary,
                    ),
                  ),
                  const SizedBox(height: AppDimensions.paddingXS),
                  Text(
                    type['description'],
                    style: AppTextStyles.bodySmall,
                  ),
                ],
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '${type['rate']}%',
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.success,
                  ),
                ),
                Text(
                  'năm',
                  style: AppTextStyles.bodySmall,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAccountForm() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.backgroundPrimary,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Thông tin tài khoản',
            style: AppTextStyles.heading4,
          ),
          const SizedBox(height: AppDimensions.paddingL),
          
          // Account Name
          Text('Tên tài khoản', style: AppTextStyles.label),
          const SizedBox(height: AppDimensions.paddingS),
          CustomTextField(
            controller: _nameController,
            hintText: 'Nhập tên tài khoản tiết kiệm',
            prefixIcon: Icons.account_balance_wallet,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Vui lòng nhập tên tài khoản';
              }
              return null;
            },
          ),
          
          const SizedBox(height: AppDimensions.paddingL),
          
          // Initial Amount
          Text('Số tiền gửi ban đầu', style: AppTextStyles.label),
          const SizedBox(height: AppDimensions.paddingS),
          CustomTextField(
            controller: _amountController,
            hintText: 'Nhập số tiền',
            keyboardType: TextInputType.number,
            prefixIcon: Icons.attach_money,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Vui lòng nhập số tiền';
              }
              final amount = double.tryParse(value);
              if (amount == null || amount < 100000) {
                return 'Số tiền tối thiểu là 100,000 VNĐ';
              }
              return null;
            },
          ),
          
          if (_selectedType == SavingsType.automatic || 
              _selectedType == SavingsType.goalBased) ...[
            const SizedBox(height: AppDimensions.paddingL),
            Text('Số tiền gửi hàng tháng', style: AppTextStyles.label),
            const SizedBox(height: AppDimensions.paddingS),
            CustomTextField(
              controller: _monthlyController,
              hintText: 'Nhập số tiền hàng tháng',
              keyboardType: TextInputType.number,
              prefixIcon: Icons.calendar_month,
              validator: (value) {
                if (_selectedType == SavingsType.automatic && 
                    (value == null || value.isEmpty)) {
                  return 'Vui lòng nhập số tiền hàng tháng';
                }
                return null;
              },
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTermSelection() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.backgroundPrimary,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Chọn kỳ hạn',
            style: AppTextStyles.heading4,
          ),
          const SizedBox(height: AppDimensions.paddingM),
          Wrap(
            spacing: AppDimensions.paddingM,
            children: _terms.map((term) {
              final isSelected = _selectedTerm == term;
              return GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedTerm = term;
                  });
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppDimensions.paddingL,
                    vertical: AppDimensions.paddingM,
                  ),
                  decoration: BoxDecoration(
                    color: isSelected 
                        ? AppColors.primaryBlue
                        : AppColors.backgroundGray,
                    borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  ),
                  child: Text(
                    '$term tháng',
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: isSelected 
                          ? AppColors.textWhite
                          : AppColors.textPrimary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildInterestInfo() {
    final selectedTypeData = _savingsTypes.firstWhere(
      (type) => type['type'] == _selectedType,
    );
    final amount = double.tryParse(_amountController.text) ?? 0;
    final monthlyAmount = double.tryParse(_monthlyController.text) ?? 0;
    final rate = selectedTypeData['rate'] / 100;
    
    double projectedEarnings = 0;
    if (_selectedType == SavingsType.fixedTerm) {
      projectedEarnings = amount * rate * (_selectedTerm / 12);
    } else {
      projectedEarnings = amount * rate; // Annual earnings
    }

    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.backgroundPrimary,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Thông tin lãi suất',
            style: AppTextStyles.heading4,
          ),
          const SizedBox(height: AppDimensions.paddingM),
          _buildInfoRow('Lãi suất', '${selectedTypeData['rate']}%/năm'),
          if (_selectedType == SavingsType.fixedTerm)
            _buildInfoRow('Kỳ hạn', '$_selectedTerm tháng'),
          _buildInfoRow('Lãi dự kiến', _formatCurrency(projectedEarnings)),
          if (amount > 0 && _selectedType == SavingsType.fixedTerm)
            _buildInfoRow('Tổng tiền nhận', 
                _formatCurrency(amount + projectedEarnings)),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppDimensions.paddingS),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          Text(
            value,
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.w600,
              color: AppColors.primaryBlue,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCreateButton() {
    return CustomButton(
      text: 'Mở tài khoản tiết kiệm',
      onPressed: _handleCreateAccount,
      isLoading: _isLoading,
    );
  }

  String _formatCurrency(double amount) {
    return '${amount.toStringAsFixed(0).replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]},',
    )} VNĐ';
  }

  Future<void> _handleCreateAccount() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });

      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        _showSuccessDialog();
      }
    }
  }

  void _showSuccessDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: AppColors.success.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppDimensions.radiusCircle),
              ),
              child: const Icon(
                Icons.check,
                size: 40,
                color: AppColors.success,
              ),
            ),
            const SizedBox(height: AppDimensions.paddingL),
            Text(
              'Mở tài khoản thành công!',
              style: AppTextStyles.heading4,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppDimensions.paddingM),
            Text(
              'Tài khoản tiết kiệm của bạn đã được tạo thành công.',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          CustomButton(
            text: 'Hoàn tất',
            onPressed: () {
              Navigator.of(context).pop(); // Close dialog
              Navigator.of(context).pop(); // Go back to savings screen
            },
          ),
        ],
      ),
    );
  }
}
