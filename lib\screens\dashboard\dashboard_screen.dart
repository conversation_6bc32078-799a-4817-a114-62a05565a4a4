import 'package:flutter/material.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_text_styles.dart';
import '../../constants/app_dimensions.dart';
import '../../widgets/balance_card.dart';
import '../../widgets/quick_action_button.dart';
import '../../widgets/transaction_item.dart';
import '../transactions/transfer_screen.dart';
import '../transactions/payment_screen.dart';
import '../transactions/transaction_history_screen.dart';
import '../profile/profile_screen.dart';
import '../cards/cards_screen.dart';

import '../savings/savings_screen.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  int _selectedIndex = 0;
  bool _isBalanceVisible = true;

  final List<Map<String, dynamic>> _recentTransactions = [
    {
      'title': '<PERSON>yển tiền cho <PERSON>ễ<PERSON>',
      'amount': -500000.0,
      'date': '15:30 - Hôm nay',
      'type': 'transfer',
    },
    {
      'title': 'Thanh toán hóa đơn điện',
      'amount': -1200000.0,
      'date': '09:15 - Hôm nay',
      'type': 'payment',
    },
    {
      'title': 'Nhận tiền từ Trần Thị B',
      'amount': 2000000.0,
      'date': '14:20 - Hôm qua',
      'type': 'receive',
    },
    {
      'title': 'Rút tiền ATM',
      'amount': -1000000.0,
      'date': '10:30 - Hôm qua',
      'type': 'withdraw',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundSecondary,
      body: SafeArea(child: _buildCurrentPage()),
      bottomNavigationBar: _buildBottomNavigationBar(),
    );
  }

  Widget _buildCurrentPage() {
    switch (_selectedIndex) {
      case 0:
        return _buildHomePage();
      case 1:
        return const TransactionHistoryScreen();
      case 2:
        return const CardsScreen();
      case 3:
        return const ProfileScreen();
      default:
        return _buildHomePage();
    }
  }

  Widget _buildHomePage() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          const SizedBox(height: AppDimensions.paddingL),
          _buildBalanceCard(),
          const SizedBox(height: AppDimensions.paddingL),
          _buildQuickActions(),
          const SizedBox(height: AppDimensions.paddingL),
          _buildRecentTransactions(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: const BoxDecoration(
        color: AppColors.backgroundPrimary,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(AppDimensions.radiusL),
          bottomRight: Radius.circular(AppDimensions.radiusL),
        ),
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: AppDimensions.avatarM / 2,
            backgroundColor: AppColors.primaryBlue,
            child: const Icon(
              Icons.person,
              color: AppColors.textWhite,
              size: AppDimensions.iconL,
            ),
          ),
          const SizedBox(width: AppDimensions.paddingM),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Xin chào,',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
                Text('Nguyễn Văn Nam', style: AppTextStyles.heading4),
              ],
            ),
          ),
          IconButton(
            onPressed: () {
              // Handle notifications
            },
            icon: const Icon(
              Icons.notifications_outlined,
              color: AppColors.textPrimary,
              size: AppDimensions.iconL,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBalanceCard() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingL),
      child: BalanceCard(
        balance: ********,
        accountNumber: '1234 5678 9012 3456',
        isVisible: _isBalanceVisible,
        onVisibilityToggle: () {
          setState(() {
            _isBalanceVisible = !_isBalanceVisible;
          });
        },
      ),
    );
  }

  Widget _buildQuickActions() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingL),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Dịch vụ', style: AppTextStyles.heading4),
          const SizedBox(height: AppDimensions.paddingM),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              QuickActionButton(
                icon: Icons.send,
                label: 'Chuyển tiền',
                onTap: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const TransferScreen(),
                    ),
                  );
                },
              ),
              QuickActionButton(
                icon: Icons.payment,
                label: 'Thanh toán',
                onTap: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const PaymentScreen(),
                    ),
                  );
                },
              ),
              QuickActionButton(
                icon: Icons.qr_code_scanner,
                label: 'Quét QR',
                onTap: () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('QR Scanner chỉ hoạt động trên mobile'),
                    ),
                  );
                },
              ),
              QuickActionButton(
                icon: Icons.savings,
                label: 'Tiết kiệm',
                onTap: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const SavingsScreen(),
                    ),
                  );
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildRecentTransactions() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingL),
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.backgroundPrimary,
        borderRadius: BorderRadius.circular(AppDimensions.radiusL),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('Giao dịch gần đây', style: AppTextStyles.heading4),
              TextButton(
                onPressed: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const TransactionHistoryScreen(),
                    ),
                  );
                },
                child: Text('Xem tất cả', style: AppTextStyles.link),
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.paddingM),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _recentTransactions.length,
            separatorBuilder: (context, index) =>
                const Divider(color: AppColors.borderLight, height: 1),
            itemBuilder: (context, index) {
              final transaction = _recentTransactions[index];
              return TransactionItem(
                title: transaction['title'],
                amount: transaction['amount'],
                date: transaction['date'],
                type: transaction['type'],
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildBottomNavigationBar() {
    return BottomNavigationBar(
      currentIndex: _selectedIndex,
      onTap: (index) {
        setState(() {
          _selectedIndex = index;
        });
      },
      type: BottomNavigationBarType.fixed,
      backgroundColor: AppColors.backgroundPrimary,
      selectedItemColor: AppColors.primaryBlue,
      unselectedItemColor: AppColors.textLight,
      elevation: 8,
      items: const [
        BottomNavigationBarItem(icon: Icon(Icons.home), label: 'Trang chủ'),
        BottomNavigationBarItem(icon: Icon(Icons.history), label: 'Lịch sử'),
        BottomNavigationBarItem(icon: Icon(Icons.credit_card), label: 'Thẻ'),
        BottomNavigationBarItem(icon: Icon(Icons.person), label: 'Tài khoản'),
      ],
    );
  }
}
