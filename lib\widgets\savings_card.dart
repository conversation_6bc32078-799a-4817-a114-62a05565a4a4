import 'package:flutter/material.dart';
import '../constants/app_colors.dart';
import '../constants/app_text_styles.dart';
import '../constants/app_dimensions.dart';
import '../models/savings_model.dart';

class SavingsCard extends StatelessWidget {
  final SavingsAccount account;
  final VoidCallback? onTap;

  const SavingsCard({
    super.key,
    required this.account,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return ListTile(
      contentPadding: const EdgeInsets.all(AppDimensions.paddingL),
      leading: Container(
        width: 56,
        height: 56,
        decoration: BoxDecoration(
          color: _getTypeColor().withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        ),
        child: Icon(
          _getTypeIcon(),
          color: _getTypeColor(),
          size: AppDimensions.iconL,
        ),
      ),
      title: Text(
        account.accountName,
        style: AppTextStyles.bodyMedium.copyWith(
          fontWeight: FontWeight.w600,
        ),
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: AppDimensions.paddingXS),
          Text(
            'STK: ${account.accountNumber}',
            style: AppTextStyles.bodySmall,
          ),
          const SizedBox(height: AppDimensions.paddingXS),
          Row(
            children: [
              Text(
                'Lãi suất: ',
                style: AppTextStyles.bodySmall,
              ),
              Text(
                '${account.interestRate}%/năm',
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.success,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          if (account.maturityDate != null) ...[
            const SizedBox(height: AppDimensions.paddingXS),
            Text(
              'Đáo hạn: ${_formatDate(account.maturityDate!)}',
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.warning,
              ),
            ),
          ],
        ],
      ),
      trailing: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            _formatCurrency(account.balance),
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.primaryBlue,
            ),
          ),
          const SizedBox(height: AppDimensions.paddingXS),
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingS,
              vertical: AppDimensions.paddingXS,
            ),
            decoration: BoxDecoration(
              color: _getStatusColor().withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppDimensions.radiusS),
            ),
            child: Text(
              _getStatusText(),
              style: AppTextStyles.bodySmall.copyWith(
                color: _getStatusColor(),
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
      onTap: onTap,
    );
  }

  IconData _getTypeIcon() {
    switch (account.type) {
      case SavingsType.regular:
        return Icons.savings;
      case SavingsType.fixedTerm:
        return Icons.schedule;
      case SavingsType.goalBased:
        return Icons.flag;
      case SavingsType.automatic:
        return Icons.autorenew;
    }
  }

  Color _getTypeColor() {
    switch (account.type) {
      case SavingsType.regular:
        return AppColors.primaryBlue;
      case SavingsType.fixedTerm:
        return AppColors.success;
      case SavingsType.goalBased:
        return AppColors.warning;
      case SavingsType.automatic:
        return AppColors.info;
    }
  }

  Color _getStatusColor() {
    switch (account.status) {
      case SavingsStatus.active:
        return AppColors.success;
      case SavingsStatus.matured:
        return AppColors.warning;
      case SavingsStatus.closed:
        return AppColors.error;
      case SavingsStatus.pending:
        return AppColors.info;
    }
  }

  String _getStatusText() {
    switch (account.status) {
      case SavingsStatus.active:
        return 'Hoạt động';
      case SavingsStatus.matured:
        return 'Đáo hạn';
      case SavingsStatus.closed:
        return 'Đã đóng';
      case SavingsStatus.pending:
        return 'Chờ duyệt';
    }
  }

  String _formatCurrency(double amount) {
    return '${amount.toStringAsFixed(0).replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]},',
    )} VNĐ';
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }
}
