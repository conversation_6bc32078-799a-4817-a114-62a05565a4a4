import 'dart:io';
import 'package:flutter/material.dart';
import 'package:qr_code_scanner/qr_code_scanner.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_text_styles.dart';
import '../../constants/app_dimensions.dart';
import 'qr_payment_screen.dart';

class QRScannerScreen extends StatefulWidget {
  const QRScannerScreen({super.key});

  @override
  State<QRScannerScreen> createState() => _QRScannerScreenState();
}

class _QRScannerScreenState extends State<QRScannerScreen> {
  final GlobalKey qrKey = GlobalKey(debugLabel: 'QR');
  QRViewController? controller;
  Barcode? result;
  bool flashOn = false;

  @override
  void reassemble() {
    super.reassemble();
    if (Platform.isAndroid) {
      controller!.pauseCamera();
    } else if (Platform.isIOS) {
      controller!.resumeCamera();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        title: const Text('Quét mã QR'),
        backgroundColor: Colors.transparent,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _toggleFlash,
            icon: Icon(flashOn ? Icons.flash_on : Icons.flash_off),
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            flex: 4,
            child: Stack(
              children: [
                QRView(
                  key: qrKey,
                  onQRViewCreated: _onQRViewCreated,
                  overlay: QrScannerOverlayShape(
                    borderColor: AppColors.primaryBlue,
                    borderRadius: 10,
                    borderLength: 30,
                    borderWidth: 10,
                    cutOutSize: 250,
                  ),
                ),
                _buildScannerOverlay(),
              ],
            ),
          ),
          Expanded(
            flex: 1,
            child: _buildBottomSection(),
          ),
        ],
      ),
    );
  }

  Widget _buildScannerOverlay() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.5),
      ),
      child: Column(
        children: [
          const Spacer(),
          Container(
            margin: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingXL),
            child: Text(
              'Đưa mã QR vào khung hình để quét',
              style: AppTextStyles.bodyLarge.copyWith(
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(height: AppDimensions.paddingXL),
          const Spacer(),
        ],
      ),
    );
  }

  Widget _buildBottomSection() {
    return Container(
      color: AppColors.backgroundPrimary,
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      child: Column(
        children: [
          Text(
            'Quét mã QR để',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: AppDimensions.paddingM),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildQuickAction(
                icon: Icons.payment,
                label: 'Thanh toán',
                onTap: () {},
              ),
              _buildQuickAction(
                icon: Icons.send,
                label: 'Chuyển tiền',
                onTap: () {},
              ),
              _buildQuickAction(
                icon: Icons.call_received,
                label: 'Nhận tiền',
                onTap: () {},
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickAction({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 56,
            height: 56,
            decoration: BoxDecoration(
              color: AppColors.primaryBlue.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            ),
            child: Icon(
              icon,
              color: AppColors.primaryBlue,
              size: AppDimensions.iconL,
            ),
          ),
          const SizedBox(height: AppDimensions.paddingS),
          Text(
            label,
            style: AppTextStyles.bodySmall.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  void _onQRViewCreated(QRViewController controller) {
    this.controller = controller;
    controller.scannedDataStream.listen((scanData) {
      setState(() {
        result = scanData;
      });
      _handleQRResult(scanData);
    });
  }

  void _handleQRResult(Barcode scanData) {
    controller?.pauseCamera();
    
    // Parse QR data
    final qrData = scanData.code;
    if (qrData != null) {
      _processQRData(qrData);
    }
  }

  void _processQRData(String qrData) {
    try {
      // Simple QR data parsing - in real app, this would be more sophisticated
      if (qrData.startsWith('bankapp://')) {
        final uri = Uri.parse(qrData);
        final type = uri.queryParameters['type'];
        
        switch (type) {
          case 'payment':
            _navigateToPayment(uri.queryParameters);
            break;
          case 'transfer':
            _navigateToTransfer(uri.queryParameters);
            break;
          case 'receive':
            _navigateToReceive(uri.queryParameters);
            break;
          default:
            _showErrorDialog('Mã QR không hợp lệ');
        }
      } else {
        // Try to parse as payment QR
        _navigateToPayment({'amount': '', 'merchant': qrData});
      }
    } catch (e) {
      _showErrorDialog('Không thể đọc mã QR');
    }
  }

  void _navigateToPayment(Map<String, String?> params) {
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(
        builder: (context) => QRPaymentScreen(
          merchantName: params['merchant'] ?? 'Merchant',
          amount: double.tryParse(params['amount'] ?? '0') ?? 0,
          qrData: params,
        ),
      ),
    );
  }

  void _navigateToTransfer(Map<String, String?> params) {
    // Navigate to transfer screen with pre-filled data
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Chuyển tiền QR đang được phát triển'),
      ),
    );
  }

  void _navigateToReceive(Map<String, String?> params) {
    // Navigate to receive screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Nhận tiền QR đang được phát triển'),
      ),
    );
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        ),
        title: Text(
          'Lỗi',
          style: AppTextStyles.heading4,
        ),
        content: Text(
          message,
          style: AppTextStyles.bodyMedium,
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              controller?.resumeCamera();
            },
            child: Text(
              'Thử lại',
              style: AppTextStyles.link,
            ),
          ),
        ],
      ),
    );
  }

  void _toggleFlash() {
    controller?.toggleFlash();
    setState(() {
      flashOn = !flashOn;
    });
  }

  @override
  void dispose() {
    controller?.dispose();
    super.dispose();
  }
}
