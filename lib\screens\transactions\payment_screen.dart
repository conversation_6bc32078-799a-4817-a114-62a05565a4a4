import 'package:flutter/material.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_text_styles.dart';
import '../../constants/app_dimensions.dart';
import '../../widgets/custom_text_field.dart';
import '../../widgets/custom_button.dart';

class PaymentScreen extends StatefulWidget {
  const PaymentScreen({super.key});

  @override
  State<PaymentScreen> createState() => _PaymentScreenState();
}

class _PaymentScreenState extends State<PaymentScreen> {
  final _formKey = GlobalKey<FormState>();
  final _billCodeController = TextEditingController();
  final _amountController = TextEditingController();
  String _selectedService = 'electricity';
  bool _isLoading = false;

  final Map<String, Map<String, dynamic>> _services = {
    'electricity': {
      'name': 'Hóa đơn điện',
      'icon': Icons.electrical_services,
      'color': AppColors.warning,
    },
    'water': {
      'name': '<PERSON><PERSON>a đơn nước',
      'icon': Icons.water_drop,
      'color': AppColors.info,
    },
    'internet': {
      'name': 'Internet',
      'icon': Icons.wifi,
      'color': AppColors.success,
    },
    'phone': {
      'name': 'Điện thoại',
      'icon': Icons.phone,
      'color': AppColors.primaryBlue,
    },
  };

  @override
  void dispose() {
    _billCodeController.dispose();
    _amountController.dispose();
    super.dispose();
  }

  Future<void> _handlePayment() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });

      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        _showSuccessDialog();
      }
    }
  }

  void _showSuccessDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: AppColors.success.withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppDimensions.radiusCircle),
              ),
              child: const Icon(
                Icons.check,
                size: 40,
                color: AppColors.success,
              ),
            ),
            const SizedBox(height: AppDimensions.paddingL),
            Text(
              'Thanh toán thành công!',
              style: AppTextStyles.heading4,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppDimensions.paddingM),
            Text(
              'Hóa đơn của bạn đã được thanh toán thành công.',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          CustomButton(
            text: 'Hoàn tất',
            onPressed: () {
              Navigator.of(context).pop(); // Close dialog
              Navigator.of(context).pop(); // Go back to dashboard
            },
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundSecondary,
      appBar: AppBar(
        title: const Text('Thanh toán hóa đơn'),
        backgroundColor: AppColors.backgroundPrimary,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildServiceSelection(),
            _buildPaymentForm(),
          ],
        ),
      ),
    );
  }

  Widget _buildServiceSelection() {
    return Container(
      margin: const EdgeInsets.all(AppDimensions.paddingL),
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.backgroundPrimary,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Chọn loại dịch vụ',
            style: AppTextStyles.heading4,
          ),
          const SizedBox(height: AppDimensions.paddingM),
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: AppDimensions.paddingM,
              mainAxisSpacing: AppDimensions.paddingM,
              childAspectRatio: 3,
            ),
            itemCount: _services.length,
            itemBuilder: (context, index) {
              final serviceKey = _services.keys.elementAt(index);
              final service = _services[serviceKey]!;
              final isSelected = _selectedService == serviceKey;

              return GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedService = serviceKey;
                  });
                },
                child: Container(
                  padding: const EdgeInsets.all(AppDimensions.paddingM),
                  decoration: BoxDecoration(
                    color: isSelected 
                        ? AppColors.primaryBlue.withOpacity(0.1)
                        : AppColors.backgroundGray,
                    borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                    border: Border.all(
                      color: isSelected 
                          ? AppColors.primaryBlue
                          : AppColors.borderLight,
                      width: isSelected ? 2 : 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        service['icon'],
                        color: isSelected 
                            ? AppColors.primaryBlue
                            : service['color'],
                        size: AppDimensions.iconM,
                      ),
                      const SizedBox(width: AppDimensions.paddingS),
                      Expanded(
                        child: Text(
                          service['name'],
                          style: AppTextStyles.bodyMedium.copyWith(
                            color: isSelected 
                                ? AppColors.primaryBlue
                                : AppColors.textPrimary,
                            fontWeight: isSelected 
                                ? FontWeight.w600
                                : FontWeight.normal,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentForm() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingL),
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.backgroundPrimary,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Thông tin thanh toán',
              style: AppTextStyles.heading4,
            ),
            const SizedBox(height: AppDimensions.paddingL),
            
            // Bill Code
            Text('Mã hóa đơn', style: AppTextStyles.label),
            const SizedBox(height: AppDimensions.paddingS),
            CustomTextField(
              controller: _billCodeController,
              hintText: 'Nhập mã hóa đơn',
              prefixIcon: Icons.receipt,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Vui lòng nhập mã hóa đơn';
                }
                return null;
              },
            ),
            
            const SizedBox(height: AppDimensions.paddingL),
            
            // Amount
            Text('Số tiền', style: AppTextStyles.label),
            const SizedBox(height: AppDimensions.paddingS),
            CustomTextField(
              controller: _amountController,
              hintText: 'Nhập số tiền',
              keyboardType: TextInputType.number,
              prefixIcon: Icons.attach_money,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Vui lòng nhập số tiền';
                }
                final amount = double.tryParse(value);
                if (amount == null || amount <= 0) {
                  return 'Số tiền không hợp lệ';
                }
                return null;
              },
            ),
            
            const SizedBox(height: AppDimensions.paddingL),
            
            // Service Info
            Container(
              padding: const EdgeInsets.all(AppDimensions.paddingM),
              decoration: BoxDecoration(
                color: AppColors.backgroundGray,
                borderRadius: BorderRadius.circular(AppDimensions.radiusS),
              ),
              child: Row(
                children: [
                  Icon(
                    _services[_selectedService]!['icon'],
                    color: _services[_selectedService]!['color'],
                    size: AppDimensions.iconM,
                  ),
                  const SizedBox(width: AppDimensions.paddingM),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Dịch vụ được chọn',
                          style: AppTextStyles.bodySmall,
                        ),
                        Text(
                          _services[_selectedService]!['name'],
                          style: AppTextStyles.bodyMedium.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: AppDimensions.paddingXL),
            
            // Payment Button
            CustomButton(
              text: 'Thanh toán',
              onPressed: _handlePayment,
              isLoading: _isLoading,
            ),
          ],
        ),
      ),
    );
  }
}
