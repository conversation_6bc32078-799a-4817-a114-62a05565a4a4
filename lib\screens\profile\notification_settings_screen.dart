import 'package:flutter/material.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_text_styles.dart';
import '../../constants/app_dimensions.dart';

class NotificationSettingsScreen extends StatefulWidget {
  const NotificationSettingsScreen({super.key});

  @override
  State<NotificationSettingsScreen> createState() => _NotificationSettingsScreenState();
}

class _NotificationSettingsScreenState extends State<NotificationSettingsScreen> {
  bool _pushNotifications = true;
  bool _transactionAlerts = true;
  bool _promotionOffers = false;
  bool _securityAlerts = true;
  bool _accountUpdates = true;
  bool _paymentReminders = true;
  bool _marketingEmails = false;
  
  TimeOfDay _quietHoursStart = const TimeOfDay(hour: 22, minute: 0);
  TimeOfDay _quietHoursEnd = const TimeOfDay(hour: 7, minute: 0);
  bool _quietHoursEnabled = true;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundSecondary,
      appBar: AppBar(
        title: const Text('Cài đặt thông báo'),
        backgroundColor: const Color(0xFF007A33),
        foregroundColor: AppColors.textWhite,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppDimensions.paddingL),
        child: Column(
          children: [
            _buildGeneralSection(),
            const SizedBox(height: AppDimensions.paddingL),
            _buildTransactionSection(),
            const SizedBox(height: AppDimensions.paddingL),
            _buildMarketingSection(),
            const SizedBox(height: AppDimensions.paddingL),
            _buildQuietHoursSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildGeneralSection() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.textWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Thông báo chung',
            style: AppTextStyles.heading4.copyWith(
              color: const Color(0xFF007A33),
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppDimensions.paddingL),
          
          _buildSwitchItem(
            icon: Icons.notifications,
            title: 'Thông báo đẩy',
            subtitle: 'Nhận thông báo trên thiết bị',
            value: _pushNotifications,
            onChanged: (value) {
              setState(() {
                _pushNotifications = value;
              });
            },
          ),
          
          const Divider(height: 32),
          
          _buildSwitchItem(
            icon: Icons.security,
            title: 'Cảnh báo bảo mật',
            subtitle: 'Thông báo về hoạt động đáng ngờ',
            value: _securityAlerts,
            onChanged: (value) {
              setState(() {
                _securityAlerts = value;
              });
            },
          ),
          
          const Divider(height: 32),
          
          _buildSwitchItem(
            icon: Icons.account_circle,
            title: 'Cập nhật tài khoản',
            subtitle: 'Thông báo về thay đổi tài khoản',
            value: _accountUpdates,
            onChanged: (value) {
              setState(() {
                _accountUpdates = value;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionSection() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.textWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Giao dịch',
            style: AppTextStyles.heading4.copyWith(
              color: const Color(0xFF007A33),
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppDimensions.paddingL),
          
          _buildSwitchItem(
            icon: Icons.payment,
            title: 'Cảnh báo giao dịch',
            subtitle: 'Thông báo mọi giao dịch',
            value: _transactionAlerts,
            onChanged: (value) {
              setState(() {
                _transactionAlerts = value;
              });
            },
          ),
          
          const Divider(height: 32),
          
          _buildSwitchItem(
            icon: Icons.schedule,
            title: 'Nhắc nhở thanh toán',
            subtitle: 'Nhắc nhở hóa đơn sắp đến hạn',
            value: _paymentReminders,
            onChanged: (value) {
              setState(() {
                _paymentReminders = value;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildMarketingSection() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.textWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Marketing',
            style: AppTextStyles.heading4.copyWith(
              color: const Color(0xFF007A33),
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppDimensions.paddingL),
          
          _buildSwitchItem(
            icon: Icons.local_offer,
            title: 'Ưu đãi khuyến mãi',
            subtitle: 'Nhận thông báo về ưu đãi mới',
            value: _promotionOffers,
            onChanged: (value) {
              setState(() {
                _promotionOffers = value;
              });
            },
          ),
          
          const Divider(height: 32),
          
          _buildSwitchItem(
            icon: Icons.email,
            title: 'Email marketing',
            subtitle: 'Nhận email về sản phẩm mới',
            value: _marketingEmails,
            onChanged: (value) {
              setState(() {
                _marketingEmails = value;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildQuietHoursSection() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.textWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Giờ yên lặng',
            style: AppTextStyles.heading4.copyWith(
              color: const Color(0xFF007A33),
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppDimensions.paddingL),
          
          _buildSwitchItem(
            icon: Icons.bedtime,
            title: 'Bật giờ yên lặng',
            subtitle: 'Tắt thông báo trong khoảng thời gian này',
            value: _quietHoursEnabled,
            onChanged: (value) {
              setState(() {
                _quietHoursEnabled = value;
              });
            },
          ),
          
          if (_quietHoursEnabled) ...[
            const Divider(height: 32),
            
            _buildTimeItem(
              icon: Icons.bedtime,
              title: 'Bắt đầu',
              time: _quietHoursStart,
              onTap: () => _selectTime(true),
            ),
            
            const SizedBox(height: AppDimensions.paddingM),
            
            _buildTimeItem(
              icon: Icons.wb_sunny,
              title: 'Kết thúc',
              time: _quietHoursEnd,
              onTap: () => _selectTime(false),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSwitchItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return Row(
      children: [
        Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: const Color(0xFF007A33).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          ),
          child: Icon(
            icon,
            color: const Color(0xFF007A33),
            size: AppDimensions.iconM,
          ),
        ),
        const SizedBox(width: AppDimensions.paddingM),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
        ),
        Switch(
          value: value,
          onChanged: onChanged,
          activeColor: const Color(0xFF007A33),
        ),
      ],
    );
  }

  Widget _buildTimeItem({
    required IconData icon,
    required String title,
    required TimeOfDay time,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: AppDimensions.paddingS),
        child: Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: const Color(0xFF007A33).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              ),
              child: Icon(
                icon,
                color: const Color(0xFF007A33),
                size: AppDimensions.iconM,
              ),
            ),
            const SizedBox(width: AppDimensions.paddingM),
            Expanded(
              child: Text(
                title,
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            Text(
              time.format(context),
              style: AppTextStyles.bodyMedium.copyWith(
                color: const Color(0xFF007A33),
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(width: AppDimensions.paddingS),
            const Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: AppColors.textSecondary,
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _selectTime(bool isStart) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: isStart ? _quietHoursStart : _quietHoursEnd,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: const Color(0xFF007A33),
            ),
          ),
          child: child!,
        );
      },
    );
    
    if (picked != null) {
      setState(() {
        if (isStart) {
          _quietHoursStart = picked;
        } else {
          _quietHoursEnd = picked;
        }
      });
    }
  }
}
