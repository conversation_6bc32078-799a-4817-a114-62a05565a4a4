// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_application_2/constants/app_theme.dart';
import 'package:flutter_application_2/screens/auth/login_screen.dart';

void main() {
  testWidgets('LoginScreen widget test', (WidgetTester tester) async {
    // Build the login screen directly
    await tester.pumpWidget(
      MaterialApp(theme: AppTheme.lightTheme, home: const LoginScreen()),
    );

    // Verify that login screen elements are present
    expect(find.text('Chào mừng trở lại'), findsOneWidget);
    expect(find.text('<PERSON>ăng nhập để tiếp tục'), findsOneWidget);
    expect(find.text('Email'), findsOneWidget);
    expect(find.text('M<PERSON>t khẩu'), findsOneWidget);
  });
}
