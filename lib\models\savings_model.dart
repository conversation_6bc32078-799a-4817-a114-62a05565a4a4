class SavingsAccount {
  final String id;
  final String accountNumber;
  final String accountName;
  final double balance;
  final double interestRate;
  final SavingsType type;
  final DateTime openDate;
  final DateTime? maturityDate;
  final double monthlyDeposit;
  final SavingsStatus status;
  final List<SavingsTransaction> transactions;

  SavingsAccount({
    required this.id,
    required this.accountNumber,
    required this.accountName,
    required this.balance,
    required this.interestRate,
    required this.type,
    required this.openDate,
    this.maturityDate,
    required this.monthlyDeposit,
    required this.status,
    required this.transactions,
  });

  double get projectedEarnings {
    if (maturityDate == null) return 0;
    final months = maturityDate!.difference(DateTime.now()).inDays / 30;
    return balance * (interestRate / 100) * (months / 12);
  }

  factory SavingsAccount.fromJson(Map<String, dynamic> json) {
    return SavingsAccount(
      id: json['id'],
      accountNumber: json['accountNumber'],
      accountName: json['accountName'],
      balance: json['balance']?.toDouble() ?? 0.0,
      interestRate: json['interestRate']?.toDouble() ?? 0.0,
      type: SavingsType.values.firstWhere(
        (e) => e.toString() == 'SavingsType.${json['type']}',
        orElse: () => SavingsType.regular,
      ),
      openDate: DateTime.parse(json['openDate']),
      maturityDate: json['maturityDate'] != null 
          ? DateTime.parse(json['maturityDate'])
          : null,
      monthlyDeposit: json['monthlyDeposit']?.toDouble() ?? 0.0,
      status: SavingsStatus.values.firstWhere(
        (e) => e.toString() == 'SavingsStatus.${json['status']}',
        orElse: () => SavingsStatus.active,
      ),
      transactions: (json['transactions'] as List?)
          ?.map((e) => SavingsTransaction.fromJson(e))
          .toList() ?? [],
    );
  }
}

enum SavingsType {
  regular,
  fixedTerm,
  goalBased,
  automatic,
}

enum SavingsStatus {
  active,
  matured,
  closed,
  pending,
}

class SavingsTransaction {
  final String id;
  final double amount;
  final DateTime date;
  final SavingsTransactionType type;
  final String description;

  SavingsTransaction({
    required this.id,
    required this.amount,
    required this.date,
    required this.type,
    required this.description,
  });

  factory SavingsTransaction.fromJson(Map<String, dynamic> json) {
    return SavingsTransaction(
      id: json['id'],
      amount: json['amount']?.toDouble() ?? 0.0,
      date: DateTime.parse(json['date']),
      type: SavingsTransactionType.values.firstWhere(
        (e) => e.toString() == 'SavingsTransactionType.${json['type']}',
        orElse: () => SavingsTransactionType.deposit,
      ),
      description: json['description'],
    );
  }
}

enum SavingsTransactionType {
  deposit,
  withdrawal,
  interest,
  fee,
}

class InvestmentProduct {
  final String id;
  final String name;
  final String description;
  final InvestmentType type;
  final double currentPrice;
  final double changePercent;
  final double minInvestment;
  final RiskLevel riskLevel;
  final double expectedReturn;
  final String currency;

  InvestmentProduct({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    required this.currentPrice,
    required this.changePercent,
    required this.minInvestment,
    required this.riskLevel,
    required this.expectedReturn,
    required this.currency,
  });

  factory InvestmentProduct.fromJson(Map<String, dynamic> json) {
    return InvestmentProduct(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      type: InvestmentType.values.firstWhere(
        (e) => e.toString() == 'InvestmentType.${json['type']}',
        orElse: () => InvestmentType.mutualFund,
      ),
      currentPrice: json['currentPrice']?.toDouble() ?? 0.0,
      changePercent: json['changePercent']?.toDouble() ?? 0.0,
      minInvestment: json['minInvestment']?.toDouble() ?? 0.0,
      riskLevel: RiskLevel.values.firstWhere(
        (e) => e.toString() == 'RiskLevel.${json['riskLevel']}',
        orElse: () => RiskLevel.medium,
      ),
      expectedReturn: json['expectedReturn']?.toDouble() ?? 0.0,
      currency: json['currency'] ?? 'VND',
    );
  }
}

enum InvestmentType {
  mutualFund,
  stock,
  bond,
  gold,
  crypto,
}

enum RiskLevel {
  low,
  medium,
  high,
}

class Portfolio {
  final String id;
  final String name;
  final double totalValue;
  final double totalInvested;
  final double totalReturn;
  final double returnPercent;
  final List<PortfolioItem> holdings;

  Portfolio({
    required this.id,
    required this.name,
    required this.totalValue,
    required this.totalInvested,
    required this.totalReturn,
    required this.returnPercent,
    required this.holdings,
  });

  factory Portfolio.fromJson(Map<String, dynamic> json) {
    return Portfolio(
      id: json['id'],
      name: json['name'],
      totalValue: json['totalValue']?.toDouble() ?? 0.0,
      totalInvested: json['totalInvested']?.toDouble() ?? 0.0,
      totalReturn: json['totalReturn']?.toDouble() ?? 0.0,
      returnPercent: json['returnPercent']?.toDouble() ?? 0.0,
      holdings: (json['holdings'] as List?)
          ?.map((e) => PortfolioItem.fromJson(e))
          .toList() ?? [],
    );
  }
}

class PortfolioItem {
  final String productId;
  final String productName;
  final double quantity;
  final double averagePrice;
  final double currentPrice;
  final double currentValue;
  final double returnAmount;
  final double returnPercent;

  PortfolioItem({
    required this.productId,
    required this.productName,
    required this.quantity,
    required this.averagePrice,
    required this.currentPrice,
    required this.currentValue,
    required this.returnAmount,
    required this.returnPercent,
  });

  factory PortfolioItem.fromJson(Map<String, dynamic> json) {
    return PortfolioItem(
      productId: json['productId'],
      productName: json['productName'],
      quantity: json['quantity']?.toDouble() ?? 0.0,
      averagePrice: json['averagePrice']?.toDouble() ?? 0.0,
      currentPrice: json['currentPrice']?.toDouble() ?? 0.0,
      currentValue: json['currentValue']?.toDouble() ?? 0.0,
      returnAmount: json['returnAmount']?.toDouble() ?? 0.0,
      returnPercent: json['returnPercent']?.toDouble() ?? 0.0,
    );
  }
}
