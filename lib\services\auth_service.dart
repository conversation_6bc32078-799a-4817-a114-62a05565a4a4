import 'package:shared_preferences/shared_preferences.dart';

class AuthService {
  static const String _isLoggedInKey = 'is_logged_in';
  static const String _userIdKey = 'user_id';
  static const String _userNameKey = 'user_name';
  static const String _userEmailKey = 'user_email';
  static const String _loginTimeKey = 'login_time';

  static AuthService? _instance;
  static AuthService get instance => _instance ??= AuthService._();
  
  AuthService._();

  // Check if user is logged in
  Future<bool> isLoggedIn() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final isLoggedIn = prefs.getBool(_isLoggedInKey) ?? false;
      
      if (!isLoggedIn) return false;
      
      // Check if login is still valid (optional: add expiration logic)
      final loginTime = prefs.getInt(_loginTimeKey) ?? 0;
      final currentTime = DateTime.now().millisecondsSinceEpoch;
      final daysSinceLogin = (currentTime - loginTime) / (1000 * 60 * 60 * 24);
      
      // Auto logout after 30 days
      if (daysSinceLogin > 30) {
        await logout();
        return false;
      }
      
      return true;
    } catch (e) {
      return false;
    }
  }

  // Login user
  Future<bool> login({
    required String email,
    required String password,
    String? userId,
    String? userName,
  }) async {
    try {
      // In real app, this would call API to validate credentials
      // For demo, we'll accept any non-empty email/password
      if (email.isEmpty || password.isEmpty) {
        return false;
      }

      final prefs = await SharedPreferences.getInstance();
      
      await prefs.setBool(_isLoggedInKey, true);
      await prefs.setString(_userIdKey, userId ?? 'user_123');
      await prefs.setString(_userNameKey, userName ?? 'Nguyễn Văn Nam');
      await prefs.setString(_userEmailKey, email);
      await prefs.setInt(_loginTimeKey, DateTime.now().millisecondsSinceEpoch);
      
      return true;
    } catch (e) {
      return false;
    }
  }

  // Register user
  Future<bool> register({
    required String name,
    required String email,
    required String password,
    required String phone,
  }) async {
    try {
      // In real app, this would call API to create account
      // For demo, we'll accept any valid data
      if (name.isEmpty || email.isEmpty || password.isEmpty || phone.isEmpty) {
        return false;
      }

      final prefs = await SharedPreferences.getInstance();
      
      await prefs.setBool(_isLoggedInKey, true);
      await prefs.setString(_userIdKey, 'user_${DateTime.now().millisecondsSinceEpoch}');
      await prefs.setString(_userNameKey, name);
      await prefs.setString(_userEmailKey, email);
      await prefs.setInt(_loginTimeKey, DateTime.now().millisecondsSinceEpoch);
      
      return true;
    } catch (e) {
      return false;
    }
  }

  // Logout user
  Future<void> logout() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      await prefs.remove(_isLoggedInKey);
      await prefs.remove(_userIdKey);
      await prefs.remove(_userNameKey);
      await prefs.remove(_userEmailKey);
      await prefs.remove(_loginTimeKey);
    } catch (e) {
      // Handle error silently
    }
  }

  // Get current user info
  Future<Map<String, String?>> getCurrentUser() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      return {
        'id': prefs.getString(_userIdKey),
        'name': prefs.getString(_userNameKey),
        'email': prefs.getString(_userEmailKey),
      };
    } catch (e) {
      return {
        'id': null,
        'name': null,
        'email': null,
      };
    }
  }

  // Update user info
  Future<bool> updateUserInfo({
    String? name,
    String? email,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      if (name != null) {
        await prefs.setString(_userNameKey, name);
      }
      
      if (email != null) {
        await prefs.setString(_userEmailKey, email);
      }
      
      return true;
    } catch (e) {
      return false;
    }
  }

  // Check if remember me is enabled
  Future<bool> isRememberMeEnabled() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool('remember_me') ?? false;
    } catch (e) {
      return false;
    }
  }

  // Set remember me preference
  Future<void> setRememberMe(bool remember) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('remember_me', remember);
    } catch (e) {
      // Handle error silently
    }
  }

  // Get saved credentials (only if remember me is enabled)
  Future<Map<String, String?>> getSavedCredentials() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final rememberMe = prefs.getBool('remember_me') ?? false;
      
      if (!rememberMe) {
        return {'email': null, 'password': null};
      }
      
      return {
        'email': prefs.getString('saved_email'),
        'password': prefs.getString('saved_password'),
      };
    } catch (e) {
      return {'email': null, 'password': null};
    }
  }

  // Save credentials (only if remember me is enabled)
  Future<void> saveCredentials(String email, String password, bool remember) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      await prefs.setBool('remember_me', remember);
      
      if (remember) {
        await prefs.setString('saved_email', email);
        await prefs.setString('saved_password', password);
      } else {
        await prefs.remove('saved_email');
        await prefs.remove('saved_password');
      }
    } catch (e) {
      // Handle error silently
    }
  }
}
