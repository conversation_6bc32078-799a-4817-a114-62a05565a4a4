import 'package:flutter/material.dart';
import '../constants/app_colors.dart';
import '../constants/app_text_styles.dart';
import '../constants/app_dimensions.dart';
import '../models/card_model.dart';

class CardWidget extends StatelessWidget {
  final BankCard card;
  final VoidCallback? onTap;
  final bool showFullNumber;

  const CardWidget({
    super.key,
    required this.card,
    this.onTap,
    this.showFullNumber = false,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        height: 200,
        padding: const EdgeInsets.all(AppDimensions.paddingL),
        decoration: BoxDecoration(
          gradient: _getCardGradient(),
          borderRadius: BorderRadius.circular(AppDimensions.radiusL),
          boxShadow: [
            BoxShadow(
              color: AppColors.shadowMedium,
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildCardHeader(),
            const Spacer(),
            _buildCardNumber(),
            const SizedBox(height: AppDimensions.paddingM),
            _buildCardFooter(),
          ],
        ),
      ),
    );
  }

  Widget _buildCardHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              _getCardTypeText(),
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textWhite.withValues(alpha: 0.8),
              ),
            ),
            const SizedBox(height: 4),
            Text(
              card.cardType == CardType.credit 
                  ? 'Khả dụng: ${_formatCurrency(card.availableCredit)}'
                  : 'Số dư: ${_formatCurrency(card.balance)}',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textWhite,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        Row(
          children: [
            if (card.isContactless)
              Container(
                margin: const EdgeInsets.only(right: 8),
                child: Icon(
                  Icons.contactless,
                  color: AppColors.textWhite,
                  size: AppDimensions.iconM,
                ),
              ),
            Text(
              card.cardBrand,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textWhite,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildCardNumber() {
    final displayNumber = showFullNumber 
        ? _formatCardNumber(card.cardNumber)
        : card.maskedCardNumber;
        
    return Text(
      displayNumber,
      style: AppTextStyles.cardNumber.copyWith(
        fontSize: 18,
        letterSpacing: 2.0,
      ),
    );
  }

  Widget _buildCardFooter() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Chủ thẻ',
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textWhite.withValues(alpha: 0.8),
              ),
            ),
            Text(
              card.cardHolderName,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textWhite,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              'Hết hạn',
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textWhite.withValues(alpha: 0.8),
              ),
            ),
            Text(
              card.expiryDate,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textWhite,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ],
    );
  }

  LinearGradient _getCardGradient() {
    switch (card.cardType) {
      case CardType.credit:
        return const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFF667EEA), Color(0xFF764BA2)],
        );
      case CardType.debit:
        return const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFF11998E), Color(0xFF38EF7D)],
        );
      case CardType.prepaid:
        return const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFFFF6B6B), Color(0xFFFFE66D)],
        );
    }
  }

  String _getCardTypeText() {
    switch (card.cardType) {
      case CardType.debit:
        return 'Thẻ ghi nợ';
      case CardType.credit:
        return 'Thẻ tín dụng';
      case CardType.prepaid:
        return 'Thẻ trả trước';
    }
  }

  String _formatCardNumber(String number) {
    return number.replaceAllMapped(
      RegExp(r'(\d{4})(?=\d)'),
      (Match m) => '${m[1]} ',
    );
  }

  String _formatCurrency(double amount) {
    return '${amount.toStringAsFixed(0).replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]},',
    )} VNĐ';
  }
}
