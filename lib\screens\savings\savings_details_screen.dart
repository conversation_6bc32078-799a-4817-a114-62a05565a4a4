import 'package:flutter/material.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_text_styles.dart';
import '../../constants/app_dimensions.dart';
import '../../models/savings_model.dart';
import '../../widgets/custom_button.dart';

class SavingsDetailsScreen extends StatefulWidget {
  final SavingsAccount account;

  const SavingsDetailsScreen({super.key, required this.account});

  @override
  State<SavingsDetailsScreen> createState() => _SavingsDetailsScreenState();
}

class _SavingsDetailsScreenState extends State<SavingsDetailsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  // Mock transaction data
  final List<SavingsTransaction> _transactions = [
    SavingsTransaction(
      id: '1',
      amount: 2000000,
      date: DateTime.now().subtract(const Duration(days: 1)),
      type: SavingsTransactionType.deposit,
      description: '<PERSON><PERSON><PERSON> tiết kiệm tháng 12',
    ),
    SavingsTransaction(
      id: '2',
      amount: 125000,
      date: DateTime.now().subtract(const Duration(days: 30)),
      type: SavingsTransactionType.interest,
      description: 'Lãi tháng 11',
    ),
    SavingsTransaction(
      id: '3',
      amount: 2000000,
      date: DateTime.now().subtract(const Duration(days: 31)),
      type: SavingsTransactionType.deposit,
      description: 'Gửi tiết kiệm tháng 11',
    ),
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundSecondary,
      appBar: AppBar(
        title: Text(widget.account.accountName),
        backgroundColor: AppColors.backgroundPrimary,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          labelColor: AppColors.primaryBlue,
          unselectedLabelColor: AppColors.textSecondary,
          indicatorColor: AppColors.primaryBlue,
          tabs: const [
            Tab(text: 'Tổng quan'),
            Tab(text: 'Giao dịch'),
            Tab(text: 'Cài đặt'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildOverviewTab(),
          _buildTransactionsTab(),
          _buildSettingsTab(),
        ],
      ),
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      child: Column(
        children: [
          _buildBalanceCard(),
          const SizedBox(height: AppDimensions.paddingL),
          _buildAccountInfo(),
          const SizedBox(height: AppDimensions.paddingL),
          _buildInterestInfo(),
          const SizedBox(height: AppDimensions.paddingL),
          _buildQuickActions(),
        ],
      ),
    );
  }

  Widget _buildBalanceCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        gradient: AppColors.primaryGradient,
        borderRadius: BorderRadius.circular(AppDimensions.radiusL),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Số dư hiện tại',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textWhite.withValues(alpha: 0.9),
            ),
          ),
          const SizedBox(height: AppDimensions.paddingS),
          Text(
            _formatCurrency(widget.account.balance),
            style: AppTextStyles.currency.copyWith(
              color: AppColors.textWhite,
              fontSize: 32,
            ),
          ),
          const SizedBox(height: AppDimensions.paddingL),
          Row(
            children: [
              Expanded(
                child: _buildBalanceItem(
                  'Lãi dự kiến',
                  _formatCurrency(widget.account.projectedEarnings),
                  Icons.trending_up,
                ),
              ),
              Expanded(
                child: _buildBalanceItem(
                  'Lãi suất',
                  '${widget.account.interestRate}%/năm',
                  Icons.percent,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildBalanceItem(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(
          icon,
          color: AppColors.textWhite.withValues(alpha: 0.8),
          size: AppDimensions.iconM,
        ),
        const SizedBox(width: AppDimensions.paddingS),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textWhite.withValues(alpha: 0.8),
              ),
            ),
            Text(
              value,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textWhite,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildAccountInfo() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.backgroundPrimary,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Thông tin tài khoản',
            style: AppTextStyles.heading4,
          ),
          const SizedBox(height: AppDimensions.paddingM),
          _buildInfoRow('Số tài khoản', widget.account.accountNumber),
          _buildInfoRow('Loại tài khoản', _getTypeText(widget.account.type)),
          _buildInfoRow('Ngày mở', _formatDate(widget.account.openDate)),
          if (widget.account.maturityDate != null)
            _buildInfoRow('Ngày đáo hạn', _formatDate(widget.account.maturityDate!)),
          _buildInfoRow('Trạng thái', _getStatusText(widget.account.status)),
          if (widget.account.monthlyDeposit > 0)
            _buildInfoRow('Gửi hàng tháng', _formatCurrency(widget.account.monthlyDeposit)),
        ],
      ),
    );
  }

  Widget _buildInterestInfo() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.backgroundPrimary,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Thông tin lãi suất',
            style: AppTextStyles.heading4,
          ),
          const SizedBox(height: AppDimensions.paddingM),
          Row(
            children: [
              Expanded(
                child: _buildInterestCard(
                  'Lãi suất hiện tại',
                  '${widget.account.interestRate}%',
                  'năm',
                  AppColors.success,
                ),
              ),
              const SizedBox(width: AppDimensions.paddingM),
              Expanded(
                child: _buildInterestCard(
                  'Lãi tích lũy',
                  _formatCurrency(widget.account.projectedEarnings),
                  'dự kiến',
                  AppColors.info,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInterestCard(String title, String value, String subtitle, Color color) {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      child: Column(
        children: [
          Text(
            title,
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppDimensions.paddingS),
          Text(
            value,
            style: AppTextStyles.bodyLarge.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
          Text(
            subtitle,
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.backgroundPrimary,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Thao tác nhanh',
            style: AppTextStyles.heading4,
          ),
          const SizedBox(height: AppDimensions.paddingM),
          Row(
            children: [
              Expanded(
                child: CustomButton(
                  text: 'Gửi thêm',
                  onPressed: () {
                    // Handle deposit
                  },
                  icon: Icons.add,
                ),
              ),
              const SizedBox(width: AppDimensions.paddingM),
              Expanded(
                child: CustomButton(
                  text: 'Rút tiền',
                  onPressed: widget.account.type == SavingsType.fixedTerm 
                      ? null 
                      : () {
                          // Handle withdrawal
                        },
                  isOutlined: true,
                  icon: Icons.remove,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionsTab() {
    return Container(
      margin: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.backgroundPrimary,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(AppDimensions.paddingL),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Lịch sử giao dịch',
                  style: AppTextStyles.heading4,
                ),
                IconButton(
                  onPressed: () {
                    // Filter transactions
                  },
                  icon: const Icon(Icons.filter_list),
                ),
              ],
            ),
          ),
          Expanded(
            child: ListView.separated(
              padding: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingL),
              itemCount: _transactions.length,
              separatorBuilder: (context, index) => const Divider(
                color: AppColors.borderLight,
                height: 1,
              ),
              itemBuilder: (context, index) {
                final transaction = _transactions[index];
                return _buildTransactionItem(transaction);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionItem(SavingsTransaction transaction) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppDimensions.paddingM),
      child: Row(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: _getTransactionColor(transaction.type).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            ),
            child: Icon(
              _getTransactionIcon(transaction.type),
              color: _getTransactionColor(transaction.type),
              size: AppDimensions.iconM,
            ),
          ),
          const SizedBox(width: AppDimensions.paddingM),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  transaction.description,
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: AppDimensions.paddingXS),
                Text(
                  _formatDate(transaction.date),
                  style: AppTextStyles.bodySmall,
                ),
              ],
            ),
          ),
          Text(
            '${transaction.type == SavingsTransactionType.withdrawal ? '-' : '+'}${_formatCurrency(transaction.amount)}',
            style: AppTextStyles.bodyMedium.copyWith(
              color: _getTransactionColor(transaction.type),
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      child: Column(
        children: [
          _buildSettingsSection(
            'Cài đặt tài khoản',
            [
              _buildSettingItem(
                'Đổi tên tài khoản',
                'Thay đổi tên hiển thị',
                Icons.edit,
                () {},
              ),
              _buildSettingItem(
                'Thiết lập gửi tự động',
                'Cài đặt gửi tiền định kỳ',
                Icons.autorenew,
                () {},
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.paddingL),
          _buildSettingsSection(
            'Thông báo',
            [
              _buildSettingItem(
                'Thông báo lãi suất',
                'Nhận thông báo khi có lãi',
                Icons.notifications,
                () {},
              ),
              _buildSettingItem(
                'Nhắc nhở gửi tiền',
                'Nhắc nhở gửi tiền định kỳ',
                Icons.alarm,
                () {},
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.paddingL),
          _buildSettingsSection(
            'Khác',
            [
              _buildSettingItem(
                'Đóng tài khoản',
                'Đóng tài khoản tiết kiệm',
                Icons.close,
                () {},
                color: AppColors.error,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsSection(String title, List<Widget> items) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.backgroundPrimary,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(AppDimensions.paddingL),
            child: Text(
              title,
              style: AppTextStyles.heading4,
            ),
          ),
          ...items,
        ],
      ),
    );
  }

  Widget _buildSettingItem(
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap, {
    Color? color,
  }) {
    return ListTile(
      leading: Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          color: (color ?? AppColors.primaryBlue).withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        ),
        child: Icon(
          icon,
          color: color ?? AppColors.primaryBlue,
          size: AppDimensions.iconM,
        ),
      ),
      title: Text(
        title,
        style: AppTextStyles.bodyMedium.copyWith(
          fontWeight: FontWeight.w600,
          color: color,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: AppTextStyles.bodySmall,
      ),
      trailing: Icon(
        Icons.arrow_forward_ios,
        size: AppDimensions.iconS,
        color: AppColors.textLight,
      ),
      onTap: onTap,
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppDimensions.paddingS),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          Text(
            value,
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  IconData _getTransactionIcon(SavingsTransactionType type) {
    switch (type) {
      case SavingsTransactionType.deposit:
        return Icons.add;
      case SavingsTransactionType.withdrawal:
        return Icons.remove;
      case SavingsTransactionType.interest:
        return Icons.trending_up;
      case SavingsTransactionType.fee:
        return Icons.receipt;
    }
  }

  Color _getTransactionColor(SavingsTransactionType type) {
    switch (type) {
      case SavingsTransactionType.deposit:
        return AppColors.success;
      case SavingsTransactionType.withdrawal:
        return AppColors.error;
      case SavingsTransactionType.interest:
        return AppColors.info;
      case SavingsTransactionType.fee:
        return AppColors.warning;
    }
  }

  String _getTypeText(SavingsType type) {
    switch (type) {
      case SavingsType.regular:
        return 'Tiết kiệm thường';
      case SavingsType.fixedTerm:
        return 'Gửi có kỳ hạn';
      case SavingsType.goalBased:
        return 'Tiết kiệm mục tiêu';
      case SavingsType.automatic:
        return 'Tiết kiệm tự động';
    }
  }

  String _getStatusText(SavingsStatus status) {
    switch (status) {
      case SavingsStatus.active:
        return 'Hoạt động';
      case SavingsStatus.matured:
        return 'Đáo hạn';
      case SavingsStatus.closed:
        return 'Đã đóng';
      case SavingsStatus.pending:
        return 'Chờ duyệt';
    }
  }

  String _formatCurrency(double amount) {
    return '${amount.toStringAsFixed(0).replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]},',
    )} VNĐ';
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }
}
