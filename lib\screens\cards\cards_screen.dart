import 'package:flutter/material.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_text_styles.dart';
import '../../constants/app_dimensions.dart';
import '../../models/card_model.dart';
import '../../widgets/card_widget.dart';
import 'card_details_screen.dart';
import 'add_card_screen.dart';

class CardsScreen extends StatefulWidget {
  const CardsScreen({super.key});

  @override
  State<CardsScreen> createState() => _CardsScreenState();
}

class _CardsScreenState extends State<CardsScreen> {
  final PageController _pageController = PageController();
  int _currentCardIndex = 0;

  // Mock data - trong thực tế sẽ lấy từ API
  final List<BankCard> _cards = [
    BankCard(
      id: '1',
      cardNumber: '****************',
      cardHolderName: 'NGUYEN VAN NAM',
      expiryDate: '12/26',
      cvv: '123',
      cardType: CardType.debit,
      status: CardStatus.active,
      balance: ********,
      creditLimit: 0,
      availableCredit: 0,
      isBlocked: false,
      isContactless: true,
      issueDate: '01/2023',
      recentTransactions: [],
    ),
    BankCard(
      id: '2',
      cardNumber: '****************',
      cardHolderName: 'NGUYEN VAN NAM',
      expiryDate: '08/27',
      cvv: '456',
      cardType: CardType.credit,
      status: CardStatus.active,
      balance: 0,
      creditLimit: ********,
      availableCredit: ********,
      isBlocked: false,
      isContactless: true,
      issueDate: '03/2023',
      recentTransactions: [],
    ),
  ];

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundSecondary,
      appBar: AppBar(
        title: const Text('Quản lý thẻ'),
        backgroundColor: AppColors.backgroundPrimary,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(builder: (context) => const AddCardScreen()),
              );
            },
            icon: const Icon(Icons.add),
          ),
        ],
      ),
      body: Column(
        children: [
          _buildCardsCarousel(),
          _buildCardIndicators(),
          const SizedBox(height: AppDimensions.paddingL),
          _buildQuickActions(),
          const SizedBox(height: AppDimensions.paddingL),
          _buildCardInfo(),
        ],
      ),
    );
  }

  Widget _buildCardsCarousel() {
    return Container(
      height: 220,
      margin: const EdgeInsets.symmetric(vertical: AppDimensions.paddingL),
      child: PageView.builder(
        controller: _pageController,
        onPageChanged: (index) {
          setState(() {
            _currentCardIndex = index;
          });
        },
        itemCount: _cards.length,
        itemBuilder: (context, index) {
          return Container(
            margin: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingL),
            child: CardWidget(
              card: _cards[index],
              onTap: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => CardDetailsScreen(card: _cards[index]),
                  ),
                );
              },
            ),
          );
        },
      ),
    );
  }

  Widget _buildCardIndicators() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(
        _cards.length,
        (index) => AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          margin: const EdgeInsets.symmetric(horizontal: 4),
          width: _currentCardIndex == index ? 24 : 8,
          height: 8,
          decoration: BoxDecoration(
            color: _currentCardIndex == index
                ? AppColors.primaryBlue
                : AppColors.borderLight,
            borderRadius: BorderRadius.circular(4),
          ),
        ),
      ),
    );
  }

  Widget _buildQuickActions() {
    final currentCard = _cards[_currentCardIndex];
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingL),
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.backgroundPrimary,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Thao tác nhanh',
            style: AppTextStyles.heading4,
          ),
          const SizedBox(height: AppDimensions.paddingM),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildActionButton(
                icon: currentCard.isBlocked ? Icons.lock_open : Icons.lock,
                label: currentCard.isBlocked ? 'Mở khóa' : 'Khóa thẻ',
                color: currentCard.isBlocked ? AppColors.success : AppColors.error,
                onTap: () => _toggleCardLock(currentCard),
              ),
              _buildActionButton(
                icon: Icons.pin,
                label: 'Đổi PIN',
                color: AppColors.primaryBlue,
                onTap: () => _changePIN(currentCard),
              ),
              _buildActionButton(
                icon: Icons.settings,
                label: 'Hạn mức',
                color: AppColors.warning,
                onTap: () => _setLimit(currentCard),
              ),
              _buildActionButton(
                icon: Icons.history,
                label: 'Lịch sử',
                color: AppColors.info,
                onTap: () => _viewHistory(currentCard),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 56,
            height: 56,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            ),
            child: Icon(
              icon,
              color: color,
              size: AppDimensions.iconL,
            ),
          ),
          const SizedBox(height: AppDimensions.paddingS),
          Text(
            label,
            style: AppTextStyles.bodySmall.copyWith(
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildCardInfo() {
    final currentCard = _cards[_currentCardIndex];
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingL),
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.backgroundPrimary,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Thông tin thẻ',
            style: AppTextStyles.heading4,
          ),
          const SizedBox(height: AppDimensions.paddingM),
          _buildInfoRow('Loại thẻ', _getCardTypeText(currentCard.cardType)),
          _buildInfoRow('Trạng thái', _getStatusText(currentCard.status)),
          _buildInfoRow('Ngày phát hành', currentCard.issueDate),
          _buildInfoRow('Thanh toán không tiếp xúc', 
              currentCard.isContactless ? 'Có' : 'Không'),
          if (currentCard.cardType == CardType.credit) ...[
            _buildInfoRow('Hạn mức tín dụng', _formatCurrency(currentCard.creditLimit)),
            _buildInfoRow('Số dư khả dụng', _formatCurrency(currentCard.availableCredit)),
          ] else ...[
            _buildInfoRow('Số dư', _formatCurrency(currentCard.balance)),
          ],
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppDimensions.paddingS),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          Text(
            value,
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  String _getCardTypeText(CardType type) {
    switch (type) {
      case CardType.debit:
        return 'Thẻ ghi nợ';
      case CardType.credit:
        return 'Thẻ tín dụng';
      case CardType.prepaid:
        return 'Thẻ trả trước';
    }
  }

  String _getStatusText(CardStatus status) {
    switch (status) {
      case CardStatus.active:
        return 'Hoạt động';
      case CardStatus.blocked:
        return 'Bị khóa';
      case CardStatus.expired:
        return 'Hết hạn';
      case CardStatus.pending:
        return 'Chờ kích hoạt';
    }
  }

  String _formatCurrency(double amount) {
    return '${amount.toStringAsFixed(0).replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]},',
    )} VNĐ';
  }

  void _toggleCardLock(BankCard card) {
    // Implement card lock/unlock logic
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(card.isBlocked ? 'Đã mở khóa thẻ' : 'Đã khóa thẻ'),
        backgroundColor: AppColors.success,
      ),
    );
  }

  void _changePIN(BankCard card) {
    // Navigate to change PIN screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Tính năng đổi PIN đang được phát triển'),
      ),
    );
  }

  void _setLimit(BankCard card) {
    // Navigate to set limit screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Tính năng thiết lập hạn mức đang được phát triển'),
      ),
    );
  }

  void _viewHistory(BankCard card) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => CardDetailsScreen(card: card),
      ),
    );
  }
}
