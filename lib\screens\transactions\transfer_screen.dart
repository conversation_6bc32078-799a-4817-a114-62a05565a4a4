import 'package:flutter/material.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_text_styles.dart';
import '../../constants/app_dimensions.dart';
import '../../widgets/custom_text_field.dart';
import '../../widgets/custom_button.dart';

class TransferScreen extends StatefulWidget {
  const TransferScreen({super.key});

  @override
  State<TransferScreen> createState() => _TransferScreenState();
}

class _TransferScreenState extends State<TransferScreen> {
  final _formKey = GlobalKey<FormState>();
  final _accountController = TextEditingController();
  final _amountController = TextEditingController();
  final _messageController = TextEditingController();
  bool _isLoading = false;

  final List<Map<String, String>> _recentContacts = [
    {'name': 'Nguyễ<PERSON>ăn <PERSON>', 'account': '**********'},
    {'name': '<PERSON><PERSON><PERSON><PERSON>', 'account': '**********'},
    {'name': '<PERSON><PERSON>ăn <PERSON>', 'account': '**********'},
  ];

  @override
  void dispose() {
    _accountController.dispose();
    _amountController.dispose();
    _messageController.dispose();
    super.dispose();
  }

  Future<void> _handleTransfer() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });

      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        _showSuccessDialog();
      }
    }
  }

  void _showSuccessDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: AppColors.success.withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppDimensions.radiusCircle),
              ),
              child: const Icon(
                Icons.check,
                size: 40,
                color: AppColors.success,
              ),
            ),
            const SizedBox(height: AppDimensions.paddingL),
            Text(
              'Chuyển tiền thành công!',
              style: AppTextStyles.heading4,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppDimensions.paddingM),
            Text(
              'Giao dịch của bạn đã được thực hiện thành công.',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          CustomButton(
            text: 'Hoàn tất',
            onPressed: () {
              Navigator.of(context).pop(); // Close dialog
              Navigator.of(context).pop(); // Go back to dashboard
            },
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundSecondary,
      appBar: AppBar(
        title: const Text('Chuyển tiền'),
        backgroundColor: AppColors.backgroundPrimary,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildRecentContacts(),
            _buildTransferForm(),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentContacts() {
    return Container(
      margin: const EdgeInsets.all(AppDimensions.paddingL),
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.backgroundPrimary,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Người nhận gần đây',
            style: AppTextStyles.heading4,
          ),
          const SizedBox(height: AppDimensions.paddingM),
          SizedBox(
            height: 80,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _recentContacts.length,
              itemBuilder: (context, index) {
                final contact = _recentContacts[index];
                return GestureDetector(
                  onTap: () {
                    _accountController.text = contact['account']!;
                  },
                  child: Container(
                    width: 60,
                    margin: const EdgeInsets.only(right: AppDimensions.paddingM),
                    child: Column(
                      children: [
                        CircleAvatar(
                          radius: 24,
                          backgroundColor: AppColors.primaryBlue.withOpacity(0.1),
                          child: Text(
                            contact['name']![0],
                            style: AppTextStyles.bodyMedium.copyWith(
                              color: AppColors.primaryBlue,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        const SizedBox(height: AppDimensions.paddingXS),
                        Text(
                          contact['name']!.split(' ').last,
                          style: AppTextStyles.bodySmall,
                          textAlign: TextAlign.center,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTransferForm() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingL),
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.backgroundPrimary,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Thông tin chuyển tiền',
              style: AppTextStyles.heading4,
            ),
            const SizedBox(height: AppDimensions.paddingL),
            
            // Account Number
            Text('Số tài khoản người nhận', style: AppTextStyles.label),
            const SizedBox(height: AppDimensions.paddingS),
            CustomTextField(
              controller: _accountController,
              hintText: 'Nhập số tài khoản',
              keyboardType: TextInputType.number,
              prefixIcon: Icons.account_balance,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Vui lòng nhập số tài khoản';
                }
                if (value.length < 10) {
                  return 'Số tài khoản không hợp lệ';
                }
                return null;
              },
            ),
            
            const SizedBox(height: AppDimensions.paddingL),
            
            // Amount
            Text('Số tiền', style: AppTextStyles.label),
            const SizedBox(height: AppDimensions.paddingS),
            CustomTextField(
              controller: _amountController,
              hintText: 'Nhập số tiền',
              keyboardType: TextInputType.number,
              prefixIcon: Icons.attach_money,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Vui lòng nhập số tiền';
                }
                final amount = double.tryParse(value);
                if (amount == null || amount <= 0) {
                  return 'Số tiền không hợp lệ';
                }
                if (amount > 50000000) {
                  return 'Số tiền vượt quá hạn mức cho phép';
                }
                return null;
              },
            ),
            
            const SizedBox(height: AppDimensions.paddingL),
            
            // Message
            Text('Nội dung chuyển tiền', style: AppTextStyles.label),
            const SizedBox(height: AppDimensions.paddingS),
            CustomTextField(
              controller: _messageController,
              hintText: 'Nhập nội dung (tùy chọn)',
              prefixIcon: Icons.message,
              maxLines: 3,
            ),
            
            const SizedBox(height: AppDimensions.paddingXL),
            
            // Transfer Button
            CustomButton(
              text: 'Chuyển tiền',
              onPressed: _handleTransfer,
              isLoading: _isLoading,
            ),
          ],
        ),
      ),
    );
  }
}
