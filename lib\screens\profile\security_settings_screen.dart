import 'package:flutter/material.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_text_styles.dart';
import '../../constants/app_dimensions.dart';
import '../../widgets/custom_button.dart';

class SecuritySettingsScreen extends StatefulWidget {
  const SecuritySettingsScreen({super.key});

  @override
  State<SecuritySettingsScreen> createState() => _SecuritySettingsScreenState();
}

class _SecuritySettingsScreenState extends State<SecuritySettingsScreen> {
  bool _biometricEnabled = true;
  bool _smsNotification = true;
  bool _emailNotification = false;
  bool _loginNotification = true;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundSecondary,
      appBar: AppBar(
        title: const Text('Cài đặt bảo mật'),
        backgroundColor: const Color(0xFF007A33),
        foregroundColor: AppColors.textWhite,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppDimensions.paddingL),
        child: Column(
          children: [
            _buildPasswordSection(),
            const SizedBox(height: AppDimensions.paddingL),
            _buildBiometricSection(),
            const SizedBox(height: AppDimensions.paddingL),
            _buildNotificationSection(),
            const SizedBox(height: AppDimensions.paddingL),
            _buildSecurityTipsSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildPasswordSection() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.textWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Mật khẩu & PIN',
            style: AppTextStyles.heading4.copyWith(
              color: const Color(0xFF007A33),
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppDimensions.paddingL),
          
          _buildSecurityItem(
            icon: Icons.lock_outline,
            title: 'Đổi mật khẩu đăng nhập',
            subtitle: 'Cập nhật lần cuối: 2 tháng trước',
            onTap: () => _showChangePasswordDialog(),
          ),
          
          const Divider(height: 32),
          
          _buildSecurityItem(
            icon: Icons.pin,
            title: 'Đổi mã PIN giao dịch',
            subtitle: 'Mã PIN 6 số để xác thực giao dịch',
            onTap: () => _showChangePinDialog(),
          ),
        ],
      ),
    );
  }

  Widget _buildBiometricSection() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.textWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Sinh trắc học',
            style: AppTextStyles.heading4.copyWith(
              color: const Color(0xFF007A33),
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppDimensions.paddingL),
          
          _buildSwitchItem(
            icon: Icons.fingerprint,
            title: 'Đăng nhập bằng vân tay',
            subtitle: 'Sử dụng vân tay để đăng nhập nhanh',
            value: _biometricEnabled,
            onChanged: (value) {
              setState(() {
                _biometricEnabled = value;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationSection() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.textWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Thông báo bảo mật',
            style: AppTextStyles.heading4.copyWith(
              color: const Color(0xFF007A33),
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppDimensions.paddingL),
          
          _buildSwitchItem(
            icon: Icons.sms,
            title: 'Thông báo SMS',
            subtitle: 'Nhận SMS khi có giao dịch',
            value: _smsNotification,
            onChanged: (value) {
              setState(() {
                _smsNotification = value;
              });
            },
          ),
          
          const Divider(height: 32),
          
          _buildSwitchItem(
            icon: Icons.email,
            title: 'Thông báo Email',
            subtitle: 'Nhận email khi có giao dịch lớn',
            value: _emailNotification,
            onChanged: (value) {
              setState(() {
                _emailNotification = value;
              });
            },
          ),
          
          const Divider(height: 32),
          
          _buildSwitchItem(
            icon: Icons.login,
            title: 'Cảnh báo đăng nhập',
            subtitle: 'Thông báo khi có đăng nhập từ thiết bị mới',
            value: _loginNotification,
            onChanged: (value) {
              setState(() {
                _loginNotification = value;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSecurityTipsSection() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.textWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.security,
                color: const Color(0xFF007A33),
                size: 24,
              ),
              const SizedBox(width: AppDimensions.paddingM),
              Text(
                'Mẹo bảo mật',
                style: AppTextStyles.heading4.copyWith(
                  color: const Color(0xFF007A33),
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.paddingM),
          
          _buildTipItem('• Không chia sẻ mật khẩu với bất kỳ ai'),
          _buildTipItem('• Sử dụng mật khẩu mạnh và duy nhất'),
          _buildTipItem('• Đổi mật khẩu định kỳ 3-6 tháng'),
          _buildTipItem('• Không đăng nhập trên thiết bị công cộng'),
          _buildTipItem('• Luôn đăng xuất sau khi sử dụng'),
        ],
      ),
    );
  }

  Widget _buildSecurityItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: AppDimensions.paddingS),
        child: Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: const Color(0xFF007A33).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              ),
              child: Icon(
                icon,
                color: const Color(0xFF007A33),
                size: AppDimensions.iconM,
              ),
            ),
            const SizedBox(width: AppDimensions.paddingM),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: AppTextStyles.bodyMedium.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
            const Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: AppColors.textSecondary,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSwitchItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return Row(
      children: [
        Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: const Color(0xFF007A33).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          ),
          child: Icon(
            icon,
            color: const Color(0xFF007A33),
            size: AppDimensions.iconM,
          ),
        ),
        const SizedBox(width: AppDimensions.paddingM),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
        ),
        Switch(
          value: value,
          onChanged: onChanged,
          activeColor: const Color(0xFF007A33),
        ),
      ],
    );
  }

  Widget _buildTipItem(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Text(
        text,
        style: AppTextStyles.bodySmall.copyWith(
          color: AppColors.textSecondary,
        ),
      ),
    );
  }

  void _showChangePasswordDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Đổi mật khẩu'),
        content: const Text('Chức năng đổi mật khẩu sẽ được cập nhật trong phiên bản tiếp theo.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Đóng'),
          ),
        ],
      ),
    );
  }

  void _showChangePinDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Đổi mã PIN'),
        content: const Text('Chức năng đổi mã PIN sẽ được cập nhật trong phiên bản tiếp theo.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Đóng'),
          ),
        ],
      ),
    );
  }
}
