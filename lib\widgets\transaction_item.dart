import 'package:flutter/material.dart';
import '../constants/app_colors.dart';
import '../constants/app_text_styles.dart';
import '../constants/app_dimensions.dart';

class TransactionItem extends StatelessWidget {
  final String title;
  final double amount;
  final String date;
  final String type;

  const TransactionItem({
    super.key,
    required this.title,
    required this.amount,
    required this.date,
    required this.type,
  });

  IconData _getTransactionIcon() {
    switch (type) {
      case 'transfer':
        return Icons.send;
      case 'payment':
        return Icons.payment;
      case 'receive':
        return Icons.call_received;
      case 'withdraw':
        return Icons.atm;
      default:
        return Icons.account_balance_wallet;
    }
  }

  Color _getTransactionColor() {
    if (amount > 0) {
      return AppColors.success;
    } else {
      return AppColors.error;
    }
  }

  String _formatCurrency(double amount) {
    final absAmount = amount.abs();
    final formatted = absAmount.toStringAsFixed(0).replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]},',
    );
    
    if (amount > 0) {
      return '+$formatted VNĐ';
    } else {
      return '-$formatted VNĐ';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppDimensions.paddingM),
      child: Row(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: _getTransactionColor().withOpacity(0.1),
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            ),
            child: Icon(
              _getTransactionIcon(),
              color: _getTransactionColor(),
              size: AppDimensions.iconM,
            ),
          ),
          const SizedBox(width: AppDimensions.paddingM),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: AppDimensions.paddingXS),
                Text(
                  date,
                  style: AppTextStyles.bodySmall,
                ),
              ],
            ),
          ),
          Text(
            _formatCurrency(amount),
            style: AppTextStyles.bodyMedium.copyWith(
              color: _getTransactionColor(),
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }
}
