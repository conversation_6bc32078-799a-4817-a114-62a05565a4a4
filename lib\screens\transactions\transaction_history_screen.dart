import 'package:flutter/material.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_text_styles.dart';
import '../../constants/app_dimensions.dart';
import '../../widgets/transaction_item.dart';

class TransactionHistoryScreen extends StatefulWidget {
  const TransactionHistoryScreen({super.key});

  @override
  State<TransactionHistoryScreen> createState() => _TransactionHistoryScreenState();
}

class _TransactionHistoryScreenState extends State<TransactionHistoryScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  
  final List<Map<String, dynamic>> _allTransactions = [
    {
      'title': 'Chuyển tiền cho Nguyễn Văn A',
      'amount': -5000200,
      'date': '15:30 - Hôm nay',
      'type': 'transfer',
    },
    {
      'title': '<PERSON>h toán hóa đơn điện',
      'amount': -12000020,
      'date': '09:15 - Hôm nay',
      'type': 'payment',
    },
    {
      'title': 'Nhận tiền từ Trần <PERSON>hị B',
      'amount': 20000001123,
      'date': '14:20 - Hôm qua',
      'type': 'receive',
    },
    {
      'title': 'Rút tiền ATM',
      'amount': -1000000,
      'date': '10:30 - Hôm qua',
      'type': 'withdraw',
    },
    {
      'title': 'Chuyển tiền cho Lê Văn C',
      'amount': -750000,
      'date': '16:45 - 2 ngày trước',
      'type': 'transfer',
    },
    {
      'title': 'Thanh toán hóa đơn nước',
      'amount': -350000,
      'date': '11:20 - 2 ngày trước',
      'type': 'payment',
    },
    {
      'title': 'Nhận lương',
      'amount': 15000000,
      'date': '08:00 - 3 ngày trước',
      'type': 'receive',
    },
    {
      'title': 'Thanh toán internet',
      'amount': -500000,
      'date': '19:30 - 4 ngày trước',
      'type': 'payment',
    },
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  List<Map<String, dynamic>> _getFilteredTransactions(String filter) {
    switch (filter) {
      case 'income':
        return _allTransactions.where((t) => t['amount'] > 0).toList();
      case 'expense':
        return _allTransactions.where((t) => t['amount'] < 0).toList();
      case 'transfer':
        return _allTransactions.where((t) => t['type'] == 'transfer').toList();
      default:
        return _allTransactions;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundSecondary,
      appBar: AppBar(
        title: const Text('Lịch sử giao dịch'),
        backgroundColor: AppColors.backgroundPrimary,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: () {
              // Handle filter
            },
            icon: const Icon(Icons.filter_list),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: AppColors.primaryBlue,
          unselectedLabelColor: AppColors.textSecondary,
          indicatorColor: AppColors.primaryBlue,
          labelStyle: AppTextStyles.bodyMedium.copyWith(
            fontWeight: FontWeight.w600,
          ),
          unselectedLabelStyle: AppTextStyles.bodyMedium,
          tabs: const [
            Tab(text: 'Tất cả'),
            Tab(text: 'Thu nhập'),
            Tab(text: 'Chi tiêu'),
            Tab(text: 'Chuyển tiền'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildTransactionList(_getFilteredTransactions('all')),
          _buildTransactionList(_getFilteredTransactions('income')),
          _buildTransactionList(_getFilteredTransactions('expense')),
          _buildTransactionList(_getFilteredTransactions('transfer')),
        ],
      ),
    );
  }

  Widget _buildTransactionList(List<Map<String, dynamic>> transactions) {
    if (transactions.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.receipt_long,
              size: 64,
              color: AppColors.textLight,
            ),
            const SizedBox(height: AppDimensions.paddingL),
            Text(
              'Không có giao dịch nào',
              style: AppTextStyles.bodyLarge.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      );
    }

    return Container(
      margin: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.backgroundPrimary,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      child: Column(
        children: [
          // Summary Card
          Container(
            padding: const EdgeInsets.all(AppDimensions.paddingL),
            decoration: BoxDecoration(
              gradient: AppColors.primaryGradient,
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildSummaryItem(
                  'Thu nhập',
                  _calculateTotal(transactions, true),
                  AppColors.textWhite,
                ),
                Container(
                  width: 1,
                  height: 40,
                  color: AppColors.textWhite.withOpacity(0.3),
                ),
                _buildSummaryItem(
                  'Chi tiêu',
                  _calculateTotal(transactions, false),
                  AppColors.textWhite,
                ),
              ],
            ),
          ),
          
          // Transaction List
          Expanded(
            child: ListView.separated(
              padding: const EdgeInsets.all(AppDimensions.paddingL),
              itemCount: transactions.length,
              separatorBuilder: (context, index) => const Divider(
                color: AppColors.borderLight,
                height: 1,
              ),
              itemBuilder: (context, index) {
                final transaction = transactions[index];
                return TransactionItem(
                  title: transaction['title'],
                  amount: transaction['amount'],
                  date: transaction['date'],
                  type: transaction['type'],
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryItem(String label, double amount, Color textColor) {
    return Column(
      children: [
        Text(
          label,
          style: AppTextStyles.bodyMedium.copyWith(
            color: textColor.withOpacity(0.8),
          ),
        ),
        const SizedBox(height: AppDimensions.paddingXS),
        Text(
          _formatCurrency(amount),
          style: AppTextStyles.heading4.copyWith(
            color: textColor,
            fontSize: 18,
          ),
        ),
      ],
    );
  }

  double _calculateTotal(List<Map<String, dynamic>> transactions, bool isIncome) {
    double total = 0;
    for (var transaction in transactions) {
      final amount = transaction['amount'] as double;
      if (isIncome && amount > 0) {
        total += amount;
      } else if (!isIncome && amount < 0) {
        total += amount.abs();
      }
    }
    return total;
  }

  String _formatCurrency(double amount) {
    return '${amount.toStringAsFixed(0).replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]},',
    )} VNĐ';
  }
}
