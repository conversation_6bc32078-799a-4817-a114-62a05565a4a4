import 'package:flutter/material.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_text_styles.dart';
import '../../constants/app_dimensions.dart';
import '../../widgets/profile_menu_item.dart';
import '../../services/auth_service.dart';
import '../auth/login_screen.dart';
import '../cards/cards_screen.dart';
import 'personal_info_screen.dart';
import 'security_settings_screen.dart';
import 'notification_settings_screen.dart';
import 'help_screen.dart';
import 'about_screen.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  String _userName = 'Nguyễn Văn Nam';
  String _userEmail = '<EMAIL>';

  @override
  void initState() {
    super.initState();
    _loadUserInfo();
  }

  Future<void> _loadUserInfo() async {
    final userInfo = await AuthService.instance.getCurrentUser();
    if (mounted) {
      setState(() {
        _userName = userInfo['name'] ?? 'Nguyễn Văn Nam';
        _userEmail = userInfo['email'] ?? '<EMAIL>';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundSecondary,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            children: [
              _buildProfileHeader(),
              const SizedBox(height: AppDimensions.paddingL),
              _buildMenuSection(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProfileHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: const BoxDecoration(
        gradient: AppColors.primaryGradient,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(AppDimensions.radiusL),
          bottomRight: Radius.circular(AppDimensions.radiusL),
        ),
      ),
      child: Column(
        children: [
          const SizedBox(height: AppDimensions.paddingL),
          CircleAvatar(
            radius: AppDimensions.avatarXL / 2,
            backgroundColor: AppColors.textWhite,
            child: const Icon(
              Icons.person,
              size: AppDimensions.iconXL,
              color: AppColors.primaryBlue,
            ),
          ),
          const SizedBox(height: AppDimensions.paddingM),
          Text(
            _userName,
            style: AppTextStyles.heading3.copyWith(color: AppColors.textWhite),
          ),
          const SizedBox(height: AppDimensions.paddingS),
          Text(
            _userEmail,
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textWhite.withValues(alpha: 0.9),
            ),
          ),
          const SizedBox(height: AppDimensions.paddingS),
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingM,
              vertical: AppDimensions.paddingS,
            ),
            decoration: BoxDecoration(
              color: AppColors.textWhite.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(AppDimensions.radiusS),
            ),
            child: Text(
              'Khách hàng VIP',
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textWhite,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMenuSection(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.backgroundPrimary,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      child: Column(
        children: [
          ProfileMenuItem(
            icon: Icons.person_outline,
            title: 'Thông tin cá nhân',
            subtitle: 'Cập nhật thông tin tài khoản',
            onTap: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const PersonalInfoScreen(),
                ),
              );
            },
          ),
          const Divider(color: AppColors.borderLight, height: 1),
          ProfileMenuItem(
            icon: Icons.credit_card,
            title: 'Quản lý thẻ',
            subtitle: 'Xem và quản lý thẻ ngân hàng',
            onTap: () {
              Navigator.of(context).push(
                MaterialPageRoute(builder: (context) => const CardsScreen()),
              );
            },
          ),
          const Divider(color: AppColors.borderLight, height: 1),
          ProfileMenuItem(
            icon: Icons.security,
            title: 'Bảo mật',
            subtitle: 'Mật khẩu, sinh trắc học',
            onTap: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const SecuritySettingsScreen(),
                ),
              );
            },
          ),
          const Divider(color: AppColors.borderLight, height: 1),
          ProfileMenuItem(
            icon: Icons.notifications,
            title: 'Thông báo',
            subtitle: 'Cài đặt thông báo',
            onTap: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const NotificationSettingsScreen(),
                ),
              );
            },
          ),
          const Divider(color: AppColors.borderLight, height: 1),
          ProfileMenuItem(
            icon: Icons.language,
            title: 'Ngôn ngữ',
            subtitle: 'Tiếng Việt',
            onTap: () {
              _showLanguageDialog();
            },
          ),
          const Divider(color: AppColors.borderLight, height: 1),
          ProfileMenuItem(
            icon: Icons.help_outline,
            title: 'Trợ giúp',
            subtitle: 'FAQ, Liên hệ hỗ trợ',
            onTap: () {
              Navigator.of(context).push(
                MaterialPageRoute(builder: (context) => const HelpScreen()),
              );
            },
          ),
          const Divider(color: AppColors.borderLight, height: 1),
          ProfileMenuItem(
            icon: Icons.info_outline,
            title: 'Về ứng dụng',
            subtitle: 'Phiên bản 1.0.0',
            onTap: () {
              Navigator.of(context).push(
                MaterialPageRoute(builder: (context) => const AboutScreen()),
              );
            },
          ),
          const Divider(color: AppColors.borderLight, height: 1),
          ProfileMenuItem(
            icon: Icons.logout,
            title: 'Đăng xuất',
            subtitle: 'Thoát khỏi tài khoản',
            iconColor: AppColors.error,
            titleColor: AppColors.error,
            onTap: () {
              _showLogoutDialog(context);
            },
          ),
        ],
      ),
    );
  }

  void _showLogoutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        ),
        title: Text('Đăng xuất', style: AppTextStyles.heading4),
        content: Text(
          'Bạn có chắc chắn muốn đăng xuất khỏi tài khoản?',
          style: AppTextStyles.bodyMedium,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Hủy',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop(); // Close dialog

              // Logout from AuthService
              await AuthService.instance.logout();

              if (context.mounted) {
                Navigator.of(context).pushAndRemoveUntil(
                  MaterialPageRoute(builder: (context) => const LoginScreen()),
                  (route) => false,
                );
              }
            },
            child: Text(
              'Đăng xuất',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.error,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showLanguageDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Chọn ngôn ngữ'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Text('🇻🇳'),
              title: const Text('Tiếng Việt'),
              trailing: const Icon(Icons.check, color: Color(0xFF007A33)),
              onTap: () => Navigator.pop(context),
            ),
            ListTile(
              leading: const Text('🇺🇸'),
              title: const Text('English'),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text(
                      'English sẽ được hỗ trợ trong phiên bản tiếp theo',
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
