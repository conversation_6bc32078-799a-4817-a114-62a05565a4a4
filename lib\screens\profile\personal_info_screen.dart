import 'package:flutter/material.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_text_styles.dart';
import '../../constants/app_dimensions.dart';
import '../../widgets/custom_text_field.dart';
import '../../widgets/custom_button.dart';
import '../../services/auth_service.dart';

class PersonalInfoScreen extends StatefulWidget {
  const PersonalInfoScreen({super.key});

  @override
  State<PersonalInfoScreen> createState() => _PersonalInfoScreenState();
}

class _PersonalInfoScreenState extends State<PersonalInfoScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _addressController = TextEditingController();
  final _idNumberController = TextEditingController();
  final _dobController = TextEditingController();

  bool _isEditing = false;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadUserInfo();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _addressController.dispose();
    _idNumberController.dispose();
    _dobController.dispose();
    super.dispose();
  }

  Future<void> _loadUserInfo() async {
    final userInfo = await AuthService.instance.getCurrentUser();
    setState(() {
      _nameController.text = userInfo['name'] ?? 'Nguyễn Văn Nam';
      _emailController.text = userInfo['email'] ?? '<EMAIL>';
      _phoneController.text = '0987654321';
      _addressController.text = '123 Đường ABC, Quận 1, TP.HCM';
      _idNumberController.text = '123456789012';
      _dobController.text = '01/01/1990';
    });
  }

  Future<void> _saveUserInfo() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });

      try {
        // Simulate API call
        await Future.delayed(const Duration(seconds: 1));

        await AuthService.instance.updateUserInfo(
          name: _nameController.text,
          email: _emailController.text,
        );

        if (mounted) {
          setState(() {
            _isLoading = false;
            _isEditing = false;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Cập nhật thông tin thành công'),
              backgroundColor: AppColors.success,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Có lỗi xảy ra. Vui lòng thử lại.'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundSecondary,
      appBar: AppBar(
        title: const Text('Thông tin cá nhân'),
        backgroundColor: const Color(0xFF007A33),
        foregroundColor: AppColors.textWhite,
        elevation: 0,
        actions: [
          if (!_isEditing)
            IconButton(
              onPressed: () {
                setState(() {
                  _isEditing = true;
                });
              },
              icon: const Icon(Icons.edit),
            ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppDimensions.paddingL),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildInfoCard(),
              const SizedBox(height: AppDimensions.paddingL),
              if (_isEditing) ...[
                CustomButton(
                  text: 'Lưu thay đổi',
                  onPressed: _saveUserInfo,
                  isLoading: _isLoading,
                  backgroundColor: const Color(0xFF007A33),
                ),
                const SizedBox(height: AppDimensions.paddingM),
                CustomButton(
                  text: 'Hủy',
                  onPressed: () {
                    setState(() {
                      _isEditing = false;
                    });
                    _loadUserInfo();
                  },
                  isOutlined: true,
                  backgroundColor: const Color(0xFF007A33),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoCard() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.textWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Thông tin cơ bản',
            style: AppTextStyles.heading4.copyWith(
              color: const Color(0xFF007A33),
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppDimensions.paddingL),

          _buildInfoField(
            label: 'Họ và tên',
            controller: _nameController,
            enabled: _isEditing,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Vui lòng nhập họ tên';
              }
              return null;
            },
          ),

          const SizedBox(height: AppDimensions.paddingM),

          _buildInfoField(
            label: 'Email',
            controller: _emailController,
            enabled: _isEditing,
            keyboardType: TextInputType.emailAddress,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Vui lòng nhập email';
              }
              if (!RegExp(
                r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
              ).hasMatch(value)) {
                return 'Email không hợp lệ';
              }
              return null;
            },
          ),

          const SizedBox(height: AppDimensions.paddingM),

          _buildInfoField(
            label: 'Số điện thoại',
            controller: _phoneController,
            enabled: _isEditing,
            keyboardType: TextInputType.phone,
          ),

          const SizedBox(height: AppDimensions.paddingM),

          _buildInfoField(
            label: 'Ngày sinh',
            controller: _dobController,
            enabled: false, // Always disabled for security
            suffixIcon: const Icon(Icons.calendar_today, size: 20),
          ),

          const SizedBox(height: AppDimensions.paddingM),

          _buildInfoField(
            label: 'CMND/CCCD',
            controller: _idNumberController,
            enabled: false, // Always disabled for security
            suffixIcon: const Icon(Icons.security, size: 20),
          ),

          const SizedBox(height: AppDimensions.paddingM),

          _buildInfoField(
            label: 'Địa chỉ',
            controller: _addressController,
            enabled: _isEditing,
            maxLines: 2,
          ),
        ],
      ),
    );
  }

  Widget _buildInfoField({
    required String label,
    required TextEditingController controller,
    bool enabled = true,
    TextInputType? keyboardType,
    Widget? suffixIcon,
    int maxLines = 1,
    String? Function(String?)? validator,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: AppTextStyles.label.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: AppDimensions.paddingS),
        CustomTextField(
          controller: controller,
          hintText: '',
          enabled: enabled,
          keyboardType: keyboardType ?? TextInputType.text,
          suffixIcon: suffixIcon,
          maxLines: maxLines,
          validator: validator,
        ),
      ],
    );
  }
}
