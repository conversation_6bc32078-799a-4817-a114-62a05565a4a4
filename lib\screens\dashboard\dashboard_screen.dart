import 'package:flutter/material.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_text_styles.dart';
import '../../constants/app_dimensions.dart';
import '../../widgets/balance_card.dart';
import '../../widgets/transaction_item.dart';
import '../transactions/transfer_screen.dart';
import '../transactions/payment_screen.dart';
import '../transactions/transaction_history_screen.dart';
import '../profile/profile_screen.dart';
import '../cards/cards_screen.dart';

import '../savings/savings_screen.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen>
    with TickerProviderStateMixin {
  int _selectedIndex = 0;
  bool _isBalanceVisible = true;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  final List<Map<String, dynamic>> _recentTransactions = [
    {
      'title': 'Chuyển tiền cho Nguyễn Văn A',
      'amount': -500000.0,
      'date': '15:30 - Hôm nay',
      'type': 'transfer',
    },
    {
      'title': 'Thanh toán hóa đơn điện',
      'amount': -1200000.0,
      'date': '09:15 - Hôm nay',
      'type': 'payment',
    },
    {
      'title': 'Nhận tiền từ Trần Thị B',
      'amount': 2000000.0,
      'date': '14:20 - Hôm qua',
      'type': 'receive',
    },
    {
      'title': 'Rút tiền ATM',
      'amount': -1000000.0,
      'date': '10:30 - Hôm qua',
      'type': 'withdraw',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundSecondary,
      body: SafeArea(child: _buildCurrentPage()),
      bottomNavigationBar: _buildBottomNavigationBar(),
    );
  }

  Widget _buildCurrentPage() {
    switch (_selectedIndex) {
      case 0:
        return _buildHomePage();
      case 1:
        return const TransactionHistoryScreen();
      case 2:
        return const CardsScreen();
      case 3:
        return const ProfileScreen();
      default:
        return _buildHomePage();
    }
  }

  Widget _buildHomePage() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            const SizedBox(height: AppDimensions.paddingL),
            _buildBalanceCard(),
            const SizedBox(height: AppDimensions.paddingL),
            _buildQuickActions(),
            const SizedBox(height: AppDimensions.paddingL),
            _buildRecentTransactions(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFF007A33), // VietcomBank green
            Color(0xFF00A84A), // Lighter green
          ],
        ),
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(24),
          bottomRight: Radius.circular(24),
        ),
        boxShadow: [
          BoxShadow(
            color: Color(0xFF007A33),
            blurRadius: 10,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: AppDimensions.avatarM / 2,
            backgroundColor: AppColors.textWhite.withValues(alpha: 0.2),
            child: const Icon(
              Icons.person,
              color: AppColors.textWhite,
              size: AppDimensions.iconL,
            ),
          ),
          const SizedBox(width: AppDimensions.paddingM),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Xin chào,',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.textWhite.withValues(alpha: 0.8),
                  ),
                ),
                Text(
                  'Nguyễn Văn Nam',
                  style: AppTextStyles.heading4.copyWith(
                    color: AppColors.textWhite,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () {
              // Handle notifications
            },
            icon: const Icon(
              Icons.notifications_outlined,
              color: AppColors.textWhite,
              size: AppDimensions.iconL,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBalanceCard() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingL),
      child: BalanceCard(
        balance: ********,
        accountNumber: '1234 5678 9012 3456',
        isVisible: _isBalanceVisible,
        onVisibilityToggle: () {
          setState(() {
            _isBalanceVisible = !_isBalanceVisible;
          });
        },
      ),
    );
  }

  Widget _buildQuickActions() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingL),
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.textWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Dịch vụ thường dùng',
            style: AppTextStyles.heading4.copyWith(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: AppDimensions.paddingL),
          // First row
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildVCBQuickAction(
                icon: Icons.send,
                label: 'Chuyển tiền',
                color: const Color(0xFF007A33),
                onTap: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const TransferScreen(),
                    ),
                  );
                },
              ),
              _buildVCBQuickAction(
                icon: Icons.payment,
                label: 'Thanh toán',
                color: const Color(0xFF1976D2),
                onTap: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const PaymentScreen(),
                    ),
                  );
                },
              ),
              _buildVCBQuickAction(
                icon: Icons.qr_code_scanner,
                label: 'Quét QR',
                color: const Color(0xFFFF6B35),
                onTap: () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('QR Scanner chỉ hoạt động trên mobile'),
                    ),
                  );
                },
              ),
              _buildVCBQuickAction(
                icon: Icons.savings,
                label: 'Tiết kiệm',
                color: const Color(0xFF9C27B0),
                onTap: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const SavingsScreen(),
                    ),
                  );
                },
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.paddingL),
          // Second row
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildVCBQuickAction(
                icon: Icons.credit_card,
                label: 'Thẻ của tôi',
                color: const Color(0xFFE91E63),
                onTap: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const CardsScreen(),
                    ),
                  );
                },
              ),
              _buildVCBQuickAction(
                icon: Icons.history,
                label: 'Lịch sử',
                color: const Color(0xFF795548),
                onTap: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const TransactionHistoryScreen(),
                    ),
                  );
                },
              ),
              _buildVCBQuickAction(
                icon: Icons.more_horiz,
                label: 'Xem thêm',
                color: const Color(0xFF607D8B),
                onTap: () {
                  // Show more services
                },
              ),
              const SizedBox(width: 60), // Empty space for alignment
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildVCBQuickAction({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 60,
        child: Column(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: AppTextStyles.bodySmall.copyWith(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: AppColors.textPrimary,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentTransactions() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingL),
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.textWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('Giao dịch gần đây', style: AppTextStyles.heading4),
              TextButton(
                onPressed: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const TransactionHistoryScreen(),
                    ),
                  );
                },
                child: Text('Xem tất cả', style: AppTextStyles.link),
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.paddingM),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _recentTransactions.length,
            separatorBuilder: (context, index) =>
                const Divider(color: AppColors.borderLight, height: 1),
            itemBuilder: (context, index) {
              final transaction = _recentTransactions[index];
              return TransactionItem(
                title: transaction['title'],
                amount: transaction['amount'],
                date: transaction['date'],
                type: transaction['type'],
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildBottomNavigationBar() {
    return BottomNavigationBar(
      currentIndex: _selectedIndex,
      onTap: (index) {
        setState(() {
          _selectedIndex = index;
        });
      },
      type: BottomNavigationBarType.fixed,
      backgroundColor: AppColors.backgroundPrimary,
      selectedItemColor: AppColors.primaryBlue,
      unselectedItemColor: AppColors.textLight,
      elevation: 8,
      items: const [
        BottomNavigationBarItem(icon: Icon(Icons.home), label: 'Trang chủ'),
        BottomNavigationBarItem(icon: Icon(Icons.history), label: 'Lịch sử'),
        BottomNavigationBarItem(icon: Icon(Icons.credit_card), label: 'Thẻ'),
        BottomNavigationBarItem(icon: Icon(Icons.person), label: 'Tài khoản'),
      ],
    );
  }
}
