import 'package:flutter/material.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_text_styles.dart';
import '../../constants/app_dimensions.dart';
import '../../models/card_model.dart';
import '../../widgets/card_widget.dart';
import '../../widgets/transaction_item.dart';

class CardDetailsScreen extends StatefulWidget {
  final BankCard card;

  const CardDetailsScreen({super.key, required this.card});

  @override
  State<CardDetailsScreen> createState() => _CardDetailsScreenState();
}

class _CardDetailsScreenState extends State<CardDetailsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _showFullNumber = false;

  // Mock transaction data
  final List<Map<String, dynamic>> _transactions = [
    {
      'title': 'Vinmart - Nguyễn <PERSON>',
      'amount': -250000,
      'date': '14:30 - Hôm nay',
      'type': 'purchase',
    },
    {
      'title': 'ATM Techcombank',
      'amount': -500000,
      'date': '09:15 - Hôm nay',
      'type': 'withdraw',
    },
    {
      'title': 'Shopee - Hoàn tiền',
      'amount': 150000,
      'date': '16:20 - Hôm qua',
      'type': 'refund',
    },
    {
      'title': 'Grab - Đi xe',
      'amount': -85000,
      'date': '12:30 - Hôm qua',
      'type': 'purchase',
    },
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundSecondary,
      appBar: AppBar(
        title: const Text('Chi tiết thẻ'),
        backgroundColor: AppColors.backgroundPrimary,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: () {
              setState(() {
                _showFullNumber = !_showFullNumber;
              });
            },
            icon: Icon(_showFullNumber ? Icons.visibility_off : Icons.visibility),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: AppColors.primaryBlue,
          unselectedLabelColor: AppColors.textSecondary,
          indicatorColor: AppColors.primaryBlue,
          tabs: const [
            Tab(text: 'Tổng quan'),
            Tab(text: 'Giao dịch'),
            Tab(text: 'Cài đặt'),
          ],
        ),
      ),
      body: Column(
        children: [
          _buildCardDisplay(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildOverviewTab(),
                _buildTransactionsTab(),
                _buildSettingsTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCardDisplay() {
    return Container(
      margin: const EdgeInsets.all(AppDimensions.paddingL),
      child: CardWidget(
        card: widget.card,
        showFullNumber: _showFullNumber,
      ),
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      child: Column(
        children: [
          _buildBalanceCard(),
          const SizedBox(height: AppDimensions.paddingL),
          _buildQuickStats(),
          const SizedBox(height: AppDimensions.paddingL),
          _buildRecentTransactions(),
        ],
      ),
    );
  }

  Widget _buildBalanceCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.backgroundPrimary,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      child: Column(
        children: [
          Text(
            widget.card.cardType == CardType.credit ? 'Số dư khả dụng' : 'Số dư hiện tại',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: AppDimensions.paddingS),
          Text(
            _formatCurrency(widget.card.cardType == CardType.credit 
                ? widget.card.availableCredit 
                : widget.card.balance),
            style: AppTextStyles.currency.copyWith(fontSize: 32),
          ),
          if (widget.card.cardType == CardType.credit) ...[
            const SizedBox(height: AppDimensions.paddingM),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Hạn mức tín dụng',
                  style: AppTextStyles.bodySmall,
                ),
                Text(
                  _formatCurrency(widget.card.creditLimit),
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildQuickStats() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.backgroundPrimary,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Thống kê tháng này',
            style: AppTextStyles.heading4,
          ),
          const SizedBox(height: AppDimensions.paddingM),
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  'Chi tiêu',
                  '2,450,000 VNĐ',
                  Icons.trending_down,
                  AppColors.error,
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  'Giao dịch',
                  '24 lần',
                  Icons.receipt,
                  AppColors.info,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      margin: const EdgeInsets.only(right: AppDimensions.paddingS),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: AppDimensions.iconL),
          const SizedBox(height: AppDimensions.paddingS),
          Text(
            title,
            style: AppTextStyles.bodySmall,
          ),
          Text(
            value,
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecentTransactions() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.backgroundPrimary,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Giao dịch gần đây',
                style: AppTextStyles.heading4,
              ),
              TextButton(
                onPressed: () {
                  _tabController.animateTo(1);
                },
                child: Text(
                  'Xem tất cả',
                  style: AppTextStyles.link,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.paddingM),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _transactions.take(3).length,
            separatorBuilder: (context, index) => const Divider(
              color: AppColors.borderLight,
              height: 1,
            ),
            itemBuilder: (context, index) {
              final transaction = _transactions[index];
              return TransactionItem(
                title: transaction['title'],
                amount: transaction['amount'],
                date: transaction['date'],
                type: transaction['type'],
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionsTab() {
    return Container(
      margin: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.backgroundPrimary,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(AppDimensions.paddingL),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Lịch sử giao dịch',
                  style: AppTextStyles.heading4,
                ),
                IconButton(
                  onPressed: () {
                    // Filter transactions
                  },
                  icon: const Icon(Icons.filter_list),
                ),
              ],
            ),
          ),
          Expanded(
            child: ListView.separated(
              padding: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingL),
              itemCount: _transactions.length,
              separatorBuilder: (context, index) => const Divider(
                color: AppColors.borderLight,
                height: 1,
              ),
              itemBuilder: (context, index) {
                final transaction = _transactions[index];
                return TransactionItem(
                  title: transaction['title'],
                  amount: transaction['amount'],
                  date: transaction['date'],
                  type: transaction['type'],
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      child: Column(
        children: [
          _buildSettingsSection(
            'Bảo mật',
            [
              _buildSettingItem(
                'Đổi mã PIN',
                'Thay đổi mã PIN của thẻ',
                Icons.pin,
                () {},
              ),
              _buildSettingItem(
                widget.card.isBlocked ? 'Mở khóa thẻ' : 'Khóa thẻ',
                widget.card.isBlocked ? 'Kích hoạt lại thẻ' : 'Tạm khóa thẻ',
                widget.card.isBlocked ? Icons.lock_open : Icons.lock,
                () {},
                color: widget.card.isBlocked ? AppColors.success : AppColors.error,
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.paddingL),
          _buildSettingsSection(
            'Hạn mức',
            [
              _buildSettingItem(
                'Hạn mức rút tiền',
                'Thiết lập hạn mức rút tiền hàng ngày',
                Icons.atm,
                () {},
              ),
              _buildSettingItem(
                'Hạn mức thanh toán',
                'Thiết lập hạn mức thanh toán hàng ngày',
                Icons.payment,
                () {},
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.paddingL),
          _buildSettingsSection(
            'Thông báo',
            [
              _buildSettingItem(
                'Thông báo giao dịch',
                'Nhận thông báo khi có giao dịch',
                Icons.notifications,
                () {},
              ),
              _buildSettingItem(
                'Thông báo bảo mật',
                'Cảnh báo hoạt động bất thường',
                Icons.security,
                () {},
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsSection(String title, List<Widget> items) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.backgroundPrimary,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(AppDimensions.paddingL),
            child: Text(
              title,
              style: AppTextStyles.heading4,
            ),
          ),
          ...items,
        ],
      ),
    );
  }

  Widget _buildSettingItem(
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap, {
    Color? color,
  }) {
    return ListTile(
      leading: Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          color: (color ?? AppColors.primaryBlue).withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        ),
        child: Icon(
          icon,
          color: color ?? AppColors.primaryBlue,
          size: AppDimensions.iconM,
        ),
      ),
      title: Text(
        title,
        style: AppTextStyles.bodyMedium.copyWith(
          fontWeight: FontWeight.w600,
          color: color,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: AppTextStyles.bodySmall,
      ),
      trailing: Icon(
        Icons.arrow_forward_ios,
        size: AppDimensions.iconS,
        color: AppColors.textLight,
      ),
      onTap: onTap,
    );
  }

  String _formatCurrency(double amount) {
    return '${amount.toStringAsFixed(0).replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]},',
    )} VNĐ';
  }
}
