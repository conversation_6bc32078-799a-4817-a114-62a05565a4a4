import 'package:flutter/material.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_text_styles.dart';
import '../../constants/app_dimensions.dart';
import '../../widgets/profile_menu_item.dart';
import '../auth/login_screen.dart';

class ProfileScreen extends StatelessWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundSecondary,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            children: [
              _buildProfileHeader(),
              const SizedBox(height: AppDimensions.paddingL),
              _buildMenuSection(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProfileHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: const BoxDecoration(
        gradient: AppColors.primaryGradient,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(AppDimensions.radiusL),
          bottomRight: Radius.circular(AppDimensions.radiusL),
        ),
      ),
      child: Column(
        children: [
          const SizedBox(height: AppDimensions.paddingL),
          CircleAvatar(
            radius: AppDimensions.avatarXL / 2,
            backgroundColor: AppColors.textWhite,
            child: const Icon(
              Icons.person,
              size: AppDimensions.iconXL,
              color: AppColors.primaryBlue,
            ),
          ),
          const SizedBox(height: AppDimensions.paddingM),
          Text(
            'Nguyễn Văn Nam',
            style: AppTextStyles.heading3.copyWith(color: AppColors.textWhite),
          ),
          const SizedBox(height: AppDimensions.paddingS),
          Text(
            '<EMAIL>',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textWhite.withValues(alpha: 0.9),
            ),
          ),
          const SizedBox(height: AppDimensions.paddingS),
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingM,
              vertical: AppDimensions.paddingS,
            ),
            decoration: BoxDecoration(
              color: AppColors.textWhite.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(AppDimensions.radiusS),
            ),
            child: Text(
              'Khách hàng VIP',
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textWhite,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMenuSection(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.backgroundPrimary,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      child: Column(
        children: [
          ProfileMenuItem(
            icon: Icons.person_outline,
            title: 'Thông tin cá nhân',
            subtitle: 'Cập nhật thông tin tài khoản',
            onTap: () {
              // Navigate to personal info
            },
          ),
          const Divider(color: AppColors.borderLight, height: 1),
          ProfileMenuItem(
            icon: Icons.credit_card,
            title: 'Quản lý thẻ',
            subtitle: 'Xem và quản lý thẻ ngân hàng',
            onTap: () {
              // Navigate to card management
            },
          ),
          const Divider(color: AppColors.borderLight, height: 1),
          ProfileMenuItem(
            icon: Icons.security,
            title: 'Bảo mật',
            subtitle: 'Mật khẩu, sinh trắc học',
            onTap: () {
              // Navigate to security settings
            },
          ),
          const Divider(color: AppColors.borderLight, height: 1),
          ProfileMenuItem(
            icon: Icons.notifications,
            title: 'Thông báo',
            subtitle: 'Cài đặt thông báo',
            onTap: () {
              // Navigate to notification settings
            },
          ),
          const Divider(color: AppColors.borderLight, height: 1),
          ProfileMenuItem(
            icon: Icons.language,
            title: 'Ngôn ngữ',
            subtitle: 'Tiếng Việt',
            onTap: () {
              // Navigate to language settings
            },
          ),
          const Divider(color: AppColors.borderLight, height: 1),
          ProfileMenuItem(
            icon: Icons.help_outline,
            title: 'Trợ giúp',
            subtitle: 'FAQ, Liên hệ hỗ trợ',
            onTap: () {
              // Navigate to help
            },
          ),
          const Divider(color: AppColors.borderLight, height: 1),
          ProfileMenuItem(
            icon: Icons.info_outline,
            title: 'Về ứng dụng',
            subtitle: 'Phiên bản 1.0.0',
            onTap: () {
              // Show about dialog
            },
          ),
          const Divider(color: AppColors.borderLight, height: 1),
          ProfileMenuItem(
            icon: Icons.logout,
            title: 'Đăng xuất',
            subtitle: 'Thoát khỏi tài khoản',
            iconColor: AppColors.error,
            titleColor: AppColors.error,
            onTap: () {
              _showLogoutDialog(context);
            },
          ),
        ],
      ),
    );
  }

  void _showLogoutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        ),
        title: Text('Đăng xuất', style: AppTextStyles.heading4),
        content: Text(
          'Bạn có chắc chắn muốn đăng xuất khỏi tài khoản?',
          style: AppTextStyles.bodyMedium,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Hủy',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(); // Close dialog
              Navigator.of(context).pushAndRemoveUntil(
                MaterialPageRoute(builder: (context) => const LoginScreen()),
                (route) => false,
              );
            },
            child: Text(
              'Đăng xuất',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.error,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
