import 'package:flutter/material.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_text_styles.dart';
import '../../constants/app_dimensions.dart';
import '../../widgets/custom_text_field.dart';
import '../../widgets/custom_button.dart';

class QRPaymentScreen extends StatefulWidget {
  final String merchantName;
  final double amount;
  final Map<String, String?> qrData;

  const QRPaymentScreen({
    super.key,
    required this.merchantName,
    required this.amount,
    required this.qrData,
  });

  @override
  State<QRPaymentScreen> createState() => _QRPaymentScreenState();
}

class _QRPaymentScreenState extends State<QRPaymentScreen> {
  final _amountController = TextEditingController();
  final _noteController = TextEditingController();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    if (widget.amount > 0) {
      _amountController.text = widget.amount.toStringAsFixed(0);
    }
  }

  @override
  void dispose() {
    _amountController.dispose();
    _noteController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundSecondary,
      appBar: AppBar(
        title: const Text('Thanh toán QR'),
        backgroundColor: AppColors.backgroundPrimary,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppDimensions.paddingL),
        child: Column(
          children: [
            _buildMerchantInfo(),
            const SizedBox(height: AppDimensions.paddingL),
            _buildPaymentForm(),
            const SizedBox(height: AppDimensions.paddingL),
            _buildPaymentSummary(),
            const SizedBox(height: AppDimensions.paddingXL),
            _buildPaymentButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildMerchantInfo() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.backgroundPrimary,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      child: Column(
        children: [
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: AppColors.primaryBlue.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppDimensions.radiusCircle),
            ),
            child: const Icon(
              Icons.store,
              size: AppDimensions.iconXL,
              color: AppColors.primaryBlue,
            ),
          ),
          const SizedBox(height: AppDimensions.paddingM),
          Text(
            widget.merchantName,
            style: AppTextStyles.heading3,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppDimensions.paddingS),
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingM,
              vertical: AppDimensions.paddingS,
            ),
            decoration: BoxDecoration(
              color: AppColors.success.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppDimensions.radiusS),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.verified,
                  size: AppDimensions.iconS,
                  color: AppColors.success,
                ),
                const SizedBox(width: AppDimensions.paddingXS),
                Text(
                  'Đã xác thực',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.success,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentForm() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.backgroundPrimary,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Thông tin thanh toán',
            style: AppTextStyles.heading4,
          ),
          const SizedBox(height: AppDimensions.paddingL),
          
          // Amount
          Text('Số tiền', style: AppTextStyles.label),
          const SizedBox(height: AppDimensions.paddingS),
          CustomTextField(
            controller: _amountController,
            hintText: 'Nhập số tiền',
            keyboardType: TextInputType.number,
            prefixIcon: Icons.attach_money,
            enabled: widget.amount == 0, // Disable if amount is pre-filled
          ),
          
          const SizedBox(height: AppDimensions.paddingL),
          
          // Note
          Text('Ghi chú (tùy chọn)', style: AppTextStyles.label),
          const SizedBox(height: AppDimensions.paddingS),
          CustomTextField(
            controller: _noteController,
            hintText: 'Nhập ghi chú',
            prefixIcon: Icons.note,
            maxLines: 3,
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentSummary() {
    final amount = double.tryParse(_amountController.text) ?? 0;
    final fee = amount * 0.001; // 0.1% fee
    final total = amount + fee;

    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.backgroundPrimary,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Tóm tắt thanh toán',
            style: AppTextStyles.heading4,
          ),
          const SizedBox(height: AppDimensions.paddingM),
          _buildSummaryRow('Số tiền', _formatCurrency(amount)),
          _buildSummaryRow('Phí giao dịch', _formatCurrency(fee)),
          const Divider(color: AppColors.borderLight),
          _buildSummaryRow(
            'Tổng cộng',
            _formatCurrency(total),
            isTotal: true,
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppDimensions.paddingS),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: isTotal 
                ? AppTextStyles.bodyMedium.copyWith(fontWeight: FontWeight.w600)
                : AppTextStyles.bodyMedium.copyWith(color: AppColors.textSecondary),
          ),
          Text(
            value,
            style: isTotal 
                ? AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.primaryBlue,
                  )
                : AppTextStyles.bodyMedium.copyWith(fontWeight: FontWeight.w600),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentButton() {
    return Column(
      children: [
        CustomButton(
          text: 'Xác nhận thanh toán',
          onPressed: _handlePayment,
          isLoading: _isLoading,
        ),
        const SizedBox(height: AppDimensions.paddingM),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.security,
              size: AppDimensions.iconS,
              color: AppColors.success,
            ),
            const SizedBox(width: AppDimensions.paddingXS),
            Text(
              'Giao dịch được bảo mật bởi BankApp',
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      ],
    );
  }

  String _formatCurrency(double amount) {
    return '${amount.toStringAsFixed(0).replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]},',
    )} VNĐ';
  }

  Future<void> _handlePayment() async {
    final amount = double.tryParse(_amountController.text) ?? 0;
    
    if (amount <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Vui lòng nhập số tiền hợp lệ'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    // Simulate payment processing
    await Future.delayed(const Duration(seconds: 2));

    if (mounted) {
      setState(() {
        _isLoading = false;
      });

      _showSuccessDialog();
    }
  }

  void _showSuccessDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: AppColors.success.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppDimensions.radiusCircle),
              ),
              child: const Icon(
                Icons.check,
                size: 40,
                color: AppColors.success,
              ),
            ),
            const SizedBox(height: AppDimensions.paddingL),
            Text(
              'Thanh toán thành công!',
              style: AppTextStyles.heading4,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppDimensions.paddingM),
            Text(
              'Giao dịch của bạn đã được xử lý thành công.',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          CustomButton(
            text: 'Hoàn tất',
            onPressed: () {
              Navigator.of(context).pop(); // Close dialog
              Navigator.of(context).pop(); // Go back to previous screen
              Navigator.of(context).pop(); // Go back to dashboard
            },
          ),
        ],
      ),
    );
  }
}
