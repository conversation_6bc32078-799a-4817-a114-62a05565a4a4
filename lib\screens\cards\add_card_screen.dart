import 'package:flutter/material.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_text_styles.dart';
import '../../constants/app_dimensions.dart';
import '../../widgets/custom_text_field.dart';
import '../../widgets/custom_button.dart';

class AddCardScreen extends StatefulWidget {
  const AddCardScreen({super.key});

  @override
  State<AddCardScreen> createState() => _AddCardScreenState();
}

class _AddCardScreenState extends State<AddCardScreen> {
  final _formKey = GlobalKey<FormState>();
  final _cardNumberController = TextEditingController();
  final _expiryController = TextEditingController();
  final _cvvController = TextEditingController();
  final _nameController = TextEditingController();
  bool _isLoading = false;

  @override
  void dispose() {
    _cardNumberController.dispose();
    _expiryController.dispose();
    _cvvController.dispose();
    _nameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundSecondary,
      appBar: AppBar(
        title: const Text('Thêm thẻ mới'),
        backgroundColor: AppColors.backgroundPrimary,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppDimensions.paddingL),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildCardPreview(),
              const SizedBox(height: AppDimensions.paddingXL),
              _buildCardForm(),
              const SizedBox(height: AppDimensions.paddingXL),
              _buildAddButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCardPreview() {
    return Container(
      width: double.infinity,
      height: 200,
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        gradient: AppColors.cardGradient,
        borderRadius: BorderRadius.circular(AppDimensions.radiusL),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowMedium,
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'BANKAPP',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.textWhite,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Icon(
                Icons.contactless,
                color: AppColors.textWhite,
                size: AppDimensions.iconL,
              ),
            ],
          ),
          const Spacer(),
          Text(
            _formatCardNumber(_cardNumberController.text),
            style: AppTextStyles.cardNumber.copyWith(
              fontSize: 18,
              letterSpacing: 2.0,
            ),
          ),
          const SizedBox(height: AppDimensions.paddingM),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Chủ thẻ',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.textWhite.withValues(alpha: 0.8),
                    ),
                  ),
                  Text(
                    _nameController.text.isEmpty 
                        ? 'TÊN CHỦ THẺ' 
                        : _nameController.text.toUpperCase(),
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: AppColors.textWhite,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    'Hết hạn',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.textWhite.withValues(alpha: 0.8),
                    ),
                  ),
                  Text(
                    _expiryController.text.isEmpty ? 'MM/YY' : _expiryController.text,
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: AppColors.textWhite,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCardForm() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.backgroundPrimary,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Thông tin thẻ',
            style: AppTextStyles.heading4,
          ),
          const SizedBox(height: AppDimensions.paddingL),
          
          // Card Number
          Text('Số thẻ', style: AppTextStyles.label),
          const SizedBox(height: AppDimensions.paddingS),
          CustomTextField(
            controller: _cardNumberController,
            hintText: '1234 5678 9012 3456',
            keyboardType: TextInputType.number,
            prefixIcon: Icons.credit_card,
            onChanged: (value) {
              setState(() {});
            },
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Vui lòng nhập số thẻ';
              }
              final cleanNumber = value.replaceAll(' ', '');
              if (cleanNumber.length != 16) {
                return 'Số thẻ phải có 16 chữ số';
              }
              return null;
            },
          ),
          
          const SizedBox(height: AppDimensions.paddingL),
          
          // Cardholder Name
          Text('Tên chủ thẻ', style: AppTextStyles.label),
          const SizedBox(height: AppDimensions.paddingS),
          CustomTextField(
            controller: _nameController,
            hintText: 'Nhập tên như trên thẻ',
            prefixIcon: Icons.person,
            onChanged: (value) {
              setState(() {});
            },
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Vui lòng nhập tên chủ thẻ';
              }
              return null;
            },
          ),
          
          const SizedBox(height: AppDimensions.paddingL),
          
          // Expiry and CVV
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Ngày hết hạn', style: AppTextStyles.label),
                    const SizedBox(height: AppDimensions.paddingS),
                    CustomTextField(
                      controller: _expiryController,
                      hintText: 'MM/YY',
                      keyboardType: TextInputType.number,
                      prefixIcon: Icons.calendar_today,
                      onChanged: (value) {
                        setState(() {});
                      },
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Vui lòng nhập ngày hết hạn';
                        }
                        if (!RegExp(r'^\d{2}/\d{2}$').hasMatch(value)) {
                          return 'Định dạng: MM/YY';
                        }
                        return null;
                      },
                    ),
                  ],
                ),
              ),
              const SizedBox(width: AppDimensions.paddingM),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('CVV', style: AppTextStyles.label),
                    const SizedBox(height: AppDimensions.paddingS),
                    CustomTextField(
                      controller: _cvvController,
                      hintText: '123',
                      keyboardType: TextInputType.number,
                      prefixIcon: Icons.security,
                      obscureText: true,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Vui lòng nhập CVV';
                        }
                        if (value.length != 3) {
                          return 'CVV phải có 3 chữ số';
                        }
                        return null;
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAddButton() {
    return Column(
      children: [
        CustomButton(
          text: 'Thêm thẻ',
          onPressed: _handleAddCard,
          isLoading: _isLoading,
        ),
        const SizedBox(height: AppDimensions.paddingM),
        Text(
          'Thông tin thẻ của bạn sẽ được mã hóa và bảo mật tuyệt đối',
          style: AppTextStyles.bodySmall.copyWith(
            color: AppColors.textSecondary,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  String _formatCardNumber(String number) {
    final cleanNumber = number.replaceAll(' ', '');
    if (cleanNumber.isEmpty) return '•••• •••• •••• ••••';
    
    String formatted = '';
    for (int i = 0; i < cleanNumber.length; i++) {
      if (i > 0 && i % 4 == 0) {
        formatted += ' ';
      }
      formatted += cleanNumber[i];
    }
    
    // Pad with dots if less than 16 digits
    while (formatted.replaceAll(' ', '').length < 16) {
      if (formatted.length > 0 && formatted.length % 5 == 4) {
        formatted += ' ';
      }
      formatted += '•';
    }
    
    return formatted;
  }

  Future<void> _handleAddCard() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });

      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        _showSuccessDialog();
      }
    }
  }

  void _showSuccessDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: AppColors.success.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppDimensions.radiusCircle),
              ),
              child: const Icon(
                Icons.check,
                size: 40,
                color: AppColors.success,
              ),
            ),
            const SizedBox(height: AppDimensions.paddingL),
            Text(
              'Thêm thẻ thành công!',
              style: AppTextStyles.heading4,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppDimensions.paddingM),
            Text(
              'Thẻ của bạn đã được thêm vào tài khoản và sẵn sàng sử dụng.',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          CustomButton(
            text: 'Hoàn tất',
            onPressed: () {
              Navigator.of(context).pop(); // Close dialog
              Navigator.of(context).pop(); // Go back to cards screen
            },
          ),
        ],
      ),
    );
  }
}
