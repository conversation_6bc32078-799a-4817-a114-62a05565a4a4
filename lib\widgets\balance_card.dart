import 'package:flutter/material.dart';
import '../constants/app_colors.dart';
import '../constants/app_text_styles.dart';
import '../constants/app_dimensions.dart';

class BalanceCard extends StatelessWidget {
  final double balance;
  final String accountNumber;
  final bool isVisible;
  final VoidCallback onVisibilityToggle;

  const BalanceCard({
    super.key,
    required this.balance,
    required this.accountNumber,
    required this.isVisible,
    required this.onVisibilityToggle,
  });

  String _formatCurrency(double amount) {
    return '${amount.toStringAsFixed(0).replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]},',
    )} VNĐ';
  }

  String _formatAccountNumber(String number) {
    return number.replaceAllMapped(
      RegExp(r'(\d{4})(?=\d)'),
      (Match m) => '${m[1]} ',
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        gradient: AppColors.cardGradient,
        borderRadius: BorderRadius.circular(AppDimensions.radiusL),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Số dư khả dụng',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.textWhite.withOpacity(0.8),
                ),
              ),
              IconButton(
                onPressed: onVisibilityToggle,
                icon: Icon(
                  isVisible ? Icons.visibility : Icons.visibility_off,
                  color: AppColors.textWhite,
                  size: AppDimensions.iconM,
                ),
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.paddingS),
          Text(
            isVisible ? _formatCurrency(balance) : '••••••••',
            style: AppTextStyles.currency.copyWith(
              color: AppColors.textWhite,
              fontSize: 32,
            ),
          ),
          const SizedBox(height: AppDimensions.paddingL),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Số tài khoản',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.textWhite.withOpacity(0.8),
                    ),
                  ),
                  const SizedBox(height: AppDimensions.paddingXS),
                  Text(
                    isVisible ? _formatAccountNumber(accountNumber) : '•••• •••• •••• ••••',
                    style: AppTextStyles.cardNumber.copyWith(
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppDimensions.paddingM,
                  vertical: AppDimensions.paddingS,
                ),
                decoration: BoxDecoration(
                  color: AppColors.textWhite.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                ),
                child: Text(
                  'BANKAPP',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textWhite,
                    fontWeight: FontWeight.bold,
                    letterSpacing: 1.2,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
