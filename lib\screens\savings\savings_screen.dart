import 'package:flutter/material.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_text_styles.dart';
import '../../constants/app_dimensions.dart';
import '../../models/savings_model.dart';
import '../../widgets/savings_card.dart';
import 'create_savings_screen.dart';
import 'savings_details_screen.dart';

class SavingsScreen extends StatefulWidget {
  const SavingsScreen({super.key});

  @override
  State<SavingsScreen> createState() => _SavingsScreenState();
}

class _SavingsScreenState extends State<SavingsScreen> {
  // Mock data
  final List<SavingsAccount> _savingsAccounts = [
    SavingsAccount(
      id: '1',
      accountNumber: 'TK001',
      accountName: 'Tiết kiệm tích lũy',
      balance: ********,
      interestRate: 6.5,
      type: SavingsType.regular,
      openDate: DateTime(2023, 1, 15),
      monthlyDeposit: 2000000,
      status: SavingsStatus.active,
      transactions: [],
    ),
    SavingsAccount(
      id: '2',
      accountNumber: 'TK002',
      accountName: 'G<PERSON><PERSON> có kỳ hạn 12 tháng',
      balance: *********,
      interestRate: 7.2,
      type: SavingsType.fixedTerm,
      openDate: DateTime(2023, 6, 1),
      maturityDate: DateTime(2024, 6, 1),
      monthlyDeposit: 0,
      status: SavingsStatus.active,
      transactions: [],
    ),
    SavingsAccount(
      id: '3',
      accountNumber: 'TK003',
      accountName: 'Mục tiêu mua nhà',
      balance: ********,
      interestRate: 6.0,
      type: SavingsType.goalBased,
      openDate: DateTime(2023, 3, 10),
      monthlyDeposit: 5000000,
      status: SavingsStatus.active,
      transactions: [],
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundSecondary,
      appBar: AppBar(
        title: const Text('Tiết kiệm'),
        backgroundColor: AppColors.backgroundPrimary,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(builder: (context) => const CreateSavingsScreen()),
              );
            },
            icon: const Icon(Icons.add),
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            _buildSummaryCard(),
            const SizedBox(height: AppDimensions.paddingL),
            _buildSavingsAccounts(),
            const SizedBox(height: AppDimensions.paddingL),
            _buildQuickActions(),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryCard() {
    final totalBalance = _savingsAccounts.fold<double>(
      0, (sum, account) => sum + account.balance);
    final totalEarnings = _savingsAccounts.fold<double>(
      0, (sum, account) => sum + account.projectedEarnings);

    return Container(
      margin: const EdgeInsets.all(AppDimensions.paddingL),
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        gradient: AppColors.primaryGradient,
        borderRadius: BorderRadius.circular(AppDimensions.radiusL),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Tổng tiết kiệm',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textWhite.withValues(alpha: 0.9),
            ),
          ),
          const SizedBox(height: AppDimensions.paddingS),
          Text(
            _formatCurrency(totalBalance),
            style: AppTextStyles.currency.copyWith(
              color: AppColors.textWhite,
              fontSize: 28,
            ),
          ),
          const SizedBox(height: AppDimensions.paddingL),
          Row(
            children: [
              Expanded(
                child: _buildSummaryItem(
                  'Lãi dự kiến',
                  _formatCurrency(totalEarnings),
                  Icons.trending_up,
                ),
              ),
              Expanded(
                child: _buildSummaryItem(
                  'Số tài khoản',
                  '${_savingsAccounts.length}',
                  Icons.account_balance_wallet,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryItem(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(
          icon,
          color: AppColors.textWhite.withValues(alpha: 0.8),
          size: AppDimensions.iconM,
        ),
        const SizedBox(width: AppDimensions.paddingS),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textWhite.withValues(alpha: 0.8),
              ),
            ),
            Text(
              value,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textWhite,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSavingsAccounts() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.backgroundPrimary,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(AppDimensions.paddingL),
            child: Text(
              'Tài khoản tiết kiệm',
              style: AppTextStyles.heading4,
            ),
          ),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _savingsAccounts.length,
            separatorBuilder: (context, index) => const Divider(
              color: AppColors.borderLight,
              height: 1,
            ),
            itemBuilder: (context, index) {
              final account = _savingsAccounts[index];
              return SavingsCard(
                account: account,
                onTap: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => SavingsDetailsScreen(account: account),
                    ),
                  );
                },
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingL),
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.backgroundPrimary,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Thao tác nhanh',
            style: AppTextStyles.heading4,
          ),
          const SizedBox(height: AppDimensions.paddingM),
          Row(
            children: [
              Expanded(
                child: _buildActionCard(
                  'Mở sổ tiết kiệm',
                  'Tạo tài khoản tiết kiệm mới',
                  Icons.add_circle_outline,
                  AppColors.primaryBlue,
                  () {
                    Navigator.of(context).push(
                      MaterialPageRoute(builder: (context) => const CreateSavingsScreen()),
                    );
                  },
                ),
              ),
              const SizedBox(width: AppDimensions.paddingM),
              Expanded(
                child: _buildActionCard(
                  'Gửi tiết kiệm',
                  'Nạp tiền vào tài khoản',
                  Icons.savings,
                  AppColors.success,
                  () {
                    // Handle deposit
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.paddingM),
          Row(
            children: [
              Expanded(
                child: _buildActionCard(
                  'Tính lãi suất',
                  'Tính toán lãi suất dự kiến',
                  Icons.calculate,
                  AppColors.warning,
                  () {
                    // Handle interest calculation
                  },
                ),
              ),
              const SizedBox(width: AppDimensions.paddingM),
              Expanded(
                child: _buildActionCard(
                  'Lịch sử',
                  'Xem lịch sử giao dịch',
                  Icons.history,
                  AppColors.info,
                  () {
                    // Handle history
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionCard(
    String title,
    String subtitle,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          border: Border.all(
            color: color.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Icon(
              icon,
              color: color,
              size: AppDimensions.iconL,
            ),
            const SizedBox(height: AppDimensions.paddingS),
            Text(
              title,
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w600,
                color: color,
              ),
            ),
            const SizedBox(height: AppDimensions.paddingXS),
            Text(
              subtitle,
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatCurrency(double amount) {
    return '${amount.toStringAsFixed(0).replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]},',
    )} VNĐ';
  }
}
