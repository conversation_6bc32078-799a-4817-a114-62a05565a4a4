import 'package:flutter/material.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_text_styles.dart';
import '../../constants/app_dimensions.dart';

class HelpScreen extends StatefulWidget {
  const HelpScreen({super.key});

  @override
  State<HelpScreen> createState() => _HelpScreenState();
}

class _HelpScreenState extends State<HelpScreen> {
  final List<Map<String, dynamic>> _faqItems = [
    {
      'question': 'Làm thế nào để chuyển tiền?',
      'answer': '<PERSON><PERSON><PERSON> mụ<PERSON> "Chuyển tiền" trên trang chủ, nhập thông tin người nhận và số tiền cần chuyển. Xác thực bằng mã PIN để hoàn tất giao dịch.',
      'isExpanded': false,
    },
    {
      'question': 'Tôi quên mật khẩu đăng nhập, phải làm sao?',
      'answer': '<PERSON>h<PERSON><PERSON> "Quên mật khẩu" ở màn hình đăng nhập, nhập số điện thoại đã đăng ký. Hệ thống sẽ gửi mã OTP để đặt lại mật khẩu.',
      'isExpanded': false,
    },
    {
      'question': 'Làm sao để kiểm tra số dư tài khoản?',
      'answer': 'Số dư hiển thị ngay trên trang chủ sau khi đăng nhập. Bạn có thể nhấn vào biểu tượng mắt để ẩn/hiện số dư.',
      'isExpanded': false,
    },
    {
      'question': 'Tôi có thể thanh toán hóa đơn nào qua app?',
      'answer': 'App hỗ trợ thanh toán hóa đơn điện, nước, internet, điện thoại, và nhiều dịch vụ khác. Vào mục "Thanh toán" để xem danh sách đầy đủ.',
      'isExpanded': false,
    },
    {
      'question': 'Làm thế nào để mở sổ tiết kiệm?',
      'answer': 'Vào mục "Tiết kiệm", chọn "Mở sổ mới", chọn kỳ hạn và số tiền gửi. Xác thực để hoàn tất việc mở sổ.',
      'isExpanded': false,
    },
    {
      'question': 'App có an toàn không?',
      'answer': 'App sử dụng mã hóa 256-bit, xác thực 2 lớp và sinh trắc học để bảo vệ tài khoản của bạn. Tuyệt đối không chia sẻ thông tin đăng nhập.',
      'isExpanded': false,
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundSecondary,
      appBar: AppBar(
        title: const Text('Trợ giúp'),
        backgroundColor: const Color(0xFF007A33),
        foregroundColor: AppColors.textWhite,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppDimensions.paddingL),
        child: Column(
          children: [
            _buildQuickHelpSection(),
            const SizedBox(height: AppDimensions.paddingL),
            _buildFAQSection(),
            const SizedBox(height: AppDimensions.paddingL),
            _buildContactSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickHelpSection() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.textWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Trợ giúp nhanh',
            style: AppTextStyles.heading4.copyWith(
              color: const Color(0xFF007A33),
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppDimensions.paddingL),
          
          Row(
            children: [
              Expanded(
                child: _buildQuickHelpItem(
                  icon: Icons.phone,
                  title: 'Hotline',
                  subtitle: '1900 545 413',
                  onTap: () => _showContactDialog('Hotline: 1900 545 413'),
                ),
              ),
              const SizedBox(width: AppDimensions.paddingM),
              Expanded(
                child: _buildQuickHelpItem(
                  icon: Icons.chat,
                  title: 'Chat',
                  subtitle: 'Trò chuyện trực tuyến',
                  onTap: () => _showContactDialog('Chat trực tuyến sẽ được cập nhật sớm'),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: AppDimensions.paddingM),
          
          Row(
            children: [
              Expanded(
                child: _buildQuickHelpItem(
                  icon: Icons.email,
                  title: 'Email',
                  subtitle: '<EMAIL>',
                  onTap: () => _showContactDialog('Email: <EMAIL>'),
                ),
              ),
              const SizedBox(width: AppDimensions.paddingM),
              Expanded(
                child: _buildQuickHelpItem(
                  icon: Icons.location_on,
                  title: 'Chi nhánh',
                  subtitle: 'Tìm chi nhánh gần nhất',
                  onTap: () => _showContactDialog('Tính năng tìm chi nhánh sẽ được cập nhật'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickHelpItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      child: Container(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        decoration: BoxDecoration(
          color: const Color(0xFF007A33).withValues(alpha: 0.05),
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          border: Border.all(
            color: const Color(0xFF007A33).withValues(alpha: 0.1),
          ),
        ),
        child: Column(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: const Color(0xFF007A33).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              ),
              child: Icon(
                icon,
                color: const Color(0xFF007A33),
                size: AppDimensions.iconM,
              ),
            ),
            const SizedBox(height: AppDimensions.paddingS),
            Text(
              title,
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFAQSection() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.textWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Câu hỏi thường gặp',
            style: AppTextStyles.heading4.copyWith(
              color: const Color(0xFF007A33),
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppDimensions.paddingL),
          
          ExpansionPanelList(
            elevation: 0,
            expandedHeaderPadding: EdgeInsets.zero,
            children: _faqItems.map<ExpansionPanel>((item) {
              return ExpansionPanel(
                headerBuilder: (context, isExpanded) {
                  return ListTile(
                    title: Text(
                      item['question'],
                      style: AppTextStyles.bodyMedium.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  );
                },
                body: Padding(
                  padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                  child: Text(
                    item['answer'],
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ),
                isExpanded: item['isExpanded'],
              );
            }).toList(),
            expansionCallback: (panelIndex, isExpanded) {
              setState(() {
                _faqItems[panelIndex]['isExpanded'] = !isExpanded;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildContactSection() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.textWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Liên hệ hỗ trợ',
            style: AppTextStyles.heading4.copyWith(
              color: const Color(0xFF007A33),
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppDimensions.paddingL),
          
          _buildContactItem(
            icon: Icons.access_time,
            title: 'Giờ làm việc',
            subtitle: 'Thứ 2 - Chủ nhật: 7:00 - 22:00',
          ),
          
          const SizedBox(height: AppDimensions.paddingM),
          
          _buildContactItem(
            icon: Icons.language,
            title: 'Ngôn ngữ hỗ trợ',
            subtitle: 'Tiếng Việt, English',
          ),
          
          const SizedBox(height: AppDimensions.paddingM),
          
          _buildContactItem(
            icon: Icons.security,
            title: 'Bảo mật',
            subtitle: 'Tuyệt đối không chia sẻ thông tin cá nhân qua điện thoại',
          ),
        ],
      ),
    );
  }

  Widget _buildContactItem({
    required IconData icon,
    required String title,
    required String subtitle,
  }) {
    return Row(
      children: [
        Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: const Color(0xFF007A33).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          ),
          child: Icon(
            icon,
            color: const Color(0xFF007A33),
            size: AppDimensions.iconM,
          ),
        ),
        const SizedBox(width: AppDimensions.paddingM),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _showContactDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Thông tin liên hệ'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Đóng'),
          ),
        ],
      ),
    );
  }
}
